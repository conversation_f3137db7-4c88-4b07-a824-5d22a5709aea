import{H as e,n as s,s as t,L as a,U as i,r as o,M as l,f as n,h as r,w as d,i as c,o as u,j as f,m,z as h,t as p,l as g,F as y,x as _,I as C,y as v}from"./index-9TNq14KG.js";import{_ as b}from"./uni-icons.Dtv1TTQG.js";import{r as w}from"./uni-app.es.Df5Q6fJy.js";import{r as k}from"./request.B3nfocmy.js";import{_ as I}from"./_plugin-vue_export-helper.BCo6x5W8.js";import"./index.B6QF5Ba_.js";const S=I({data:()=>({statusBarHeight:0,password:"",verifyCode:"",counting:0,timer:null,userInfo:{},isCodeSending:!1}),created(){const s=e();this.statusBarHeight=s.statusBarHeight},beforeDestroy(){this.timer&&(clearInterval(this.timer),this.timer=null)},onShow(){this.getUserInfo()},methods:{handleBack(){s()},async getUserInfo(){try{const e=await k({url:"/api/user/info",method:"GET"});200===e.code&&(this.userInfo=e.data)}catch(e){console.error("获取用户信息失败",e)}},async sendVerifyCode(){if(!(this.counting>0||this.isCodeSending)){this.isCodeSending=!0;try{const e=await k({url:"/api/auth/send-reset-code-email",method:"POST",params:{email:this.userInfo.email}});200===e.code?(t({title:"验证码已发送",icon:"none"}),this.counting=60,this.timer&&clearInterval(this.timer),this.timer=setInterval((()=>{this.counting>0?this.counting--:(clearInterval(this.timer),this.timer=null)}),1e3)):t({title:e.message||"发送失败",icon:"none"})}catch(e){console.error("发送验证码失败:",e),t({title:"发送失败",icon:"none"})}finally{this.isCodeSending=!1}}},async handleSubmit(){if(this.password)if(this.verifyCode)try{a({title:"提交中..."});const e=await k({url:"/api/user/password/update",method:"POST",data:{password:this.password,verifyCode:this.verifyCode}});200===e.code?(t({title:"密码修改成功",icon:"success"}),setTimeout((()=>{i(),o({url:"/pages/login/index"})}),1500)):t({title:e.message||"修改失败",icon:"none"})}catch(e){t({title:e.message||"修改失败",icon:"none"})}finally{l()}else t({title:"请输入验证码",icon:"none"});else t({title:"请输入新密码",icon:"none"})},maskEmail(e){if(!e)return"";const[s,t]=e.split("@");return t?s.length<=2?"*".repeat(s.length)+"@"+t:s[0]+"*".repeat(s.length-2)+s[s.length-1]+"@"+t:e}}},[["render",function(e,s,t,a,i,o){const l=w(n("uni-icons"),b),k=c,I=_,S=C,x=v;return u(),r(k,{class:"password-container"},{default:d((()=>[f(k,{class:"custom-navbar",style:h({paddingTop:i.statusBarHeight+"px"})},{default:d((()=>[f(k,{class:"navbar-content"},{default:d((()=>[f(k,{class:"left-area",onClick:o.handleBack},{default:d((()=>[f(l,{type:"left",size:"20",color:"#fff"})])),_:1},8,["onClick"]),f(I,{class:"page-title"},{default:d((()=>[m("修改密码")])),_:1}),f(k,{class:"right-area"})])),_:1})])),_:1},8,["style"]),f(k,{class:"form-content"},{default:d((()=>[f(k,{class:"phone-info"},{default:d((()=>[f(I,{class:"label"},{default:d((()=>[m("当前绑定邮箱：")])),_:1}),f(I,{class:"phone"},{default:d((()=>[m(p(o.maskEmail(i.userInfo.email)),1)])),_:1})])),_:1}),f(k,{class:"form-item"},{default:d((()=>[f(I,{class:"form-label"},{default:d((()=>[m("登录密码")])),_:1}),f(S,{class:"form-input",type:"password",modelValue:i.password,"onUpdate:modelValue":s[0]||(s[0]=e=>i.password=e),placeholder:"请输入新登录密码","placeholder-style":"color: #695a5a;"},null,8,["modelValue"])])),_:1}),f(k,{class:"form-item"},{default:d((()=>[f(I,{class:"form-label"},{default:d((()=>[m("验证码")])),_:1}),f(k,{class:"verify-group"},{default:d((()=>[f(S,{class:"form-input",type:"text",modelValue:i.verifyCode,"onUpdate:modelValue":s[1]||(s[1]=e=>i.verifyCode=e),placeholder:"验证码","placeholder-style":"color: #695a5a;"},null,8,["modelValue"]),f(x,{class:"verify-btn",disabled:i.counting>0||i.isCodeSending,onClick:o.sendVerifyCode},{default:d((()=>[i.isCodeSending?(u(),g(y,{key:0},[m("发送中...")],64)):i.counting>0?(u(),g(y,{key:1},[m(p(i.counting)+"s",1)],64)):(u(),g(y,{key:2},[m("发送验证码")],64))])),_:1},8,["disabled","onClick"])])),_:1})])),_:1}),f(x,{class:"submit-btn",onClick:o.handleSubmit},{default:d((()=>[m("确定修改")])),_:1},8,["onClick"])])),_:1})])),_:1})}],["__scopeId","data-v-59c52c1b"]]);export{S as default};
