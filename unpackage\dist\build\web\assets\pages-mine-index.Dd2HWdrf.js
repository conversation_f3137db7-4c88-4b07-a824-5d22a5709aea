import{b as a,A as s,g as t,e,s as o,L as n,M as l,h as i,w as c,i as r,o as d,j as u,m as p,t as m,k as f,l as h,q as w,F as y,u as _,v as g,x as v,y as C,I as T}from"./index-9TNq14KG.js";import{C as k}from"./custom-navbar.DX0RdSvf.js";import{r as b}from"./request.B3nfocmy.js";import{c as B}from"./index.B6QF5Ba_.js";import{_ as P}from"./_plugin-vue_export-helper.BCo6x5W8.js";import"./uni-icons.Dtv1TTQG.js";import"./uni-app.es.Df5Q6fJy.js";const A=P({components:{CustomNavbar:k},data:()=>({userEmail:"",userNo:"",availableBalance:"0.00",copyTradeBalance:"0.00",commissionBalance:"0.00",usageFrozenBalance:"0.00",profitBalance:"0.00",inviteCode:"",userType:"",carbonPoints:"",exchangeVouchers:"",dynamicQuota:"",waitExchangeGb:"",catBalance:"0.00",isActivated:!1,commissionRate:0,assetsMenu:[{icon:"/static/icons/svg/carbon.svg",text:"我的资产",type:"carbon"},{icon:"/static/icons/svg/gift.svg",text:"我的团队",type:"gift"},{icon:"/static/icons/svg/exchange.svg",text:"收益明细",type:"exchange"},{icon:"/static/icons/svg/battery.svg",text:"人人有礼",type:"battery"}],accountList:[{name:"资金账户",type:"fund"},{name:"佣金账户",type:"commission"},{name:"跟单账户",type:"copy"},{name:"利润账户",type:"profit"}],showTransferPopup:!1,showPasswordPopup:!1,transferFrom:null,transferTo:null,transferAmount:"",payPassword:"",showFromDropdown:!1,showToDropdown:!1,todayCommission:null,upCount:null,downCount:null,statsLoading:!0,avatarUrl:"/static/tools/default.png",defaultAvatar:"/static/tools/default.png",isLeader:!1}),computed:{availableToAccounts(){if(null===this.transferFrom)return this.accountList;switch(this.accountList[this.transferFrom].type){case"fund":return this.accountList.filter((a=>"copy"===a.type));case"copy":return this.accountList.filter((a=>"fund"===a.type));case"commission":case"profit":return this.accountList.filter((a=>"fund"===a.type||"copy"===a.type));default:return[]}}},onShow(){this.loadUserInfo(),this.loadTodayStats()},methods:{formatAccount:a=>a&&""!==a?a.length>7?a.substr(0,3)+"****"+a.substr(-4):a:"",maskEmail(a){if(!a)return"";const s=a.indexOf("@");if(s<=2)return a;return a.slice(0,2)+"****"+a.slice(s-1,s)+a.slice(s)},getAvatarUrl:a=>a?a.startsWith("http")?a:B.apiBaseUrl+a:"/static/tools/default.png",onAvatarError(){this.avatarUrl=this.defaultAvatar},async loadUserInfo(){try{const t=await b({url:"/api/user/info",method:"GET"});if(200===t.code&&t.data){if(this.userEmail=t.data.email||"",this.userNo=t.data.userNo||"",this.availableBalance=t.data.availableBalance||"0.00",this.copyTradeBalance=t.data.copyTradeBalance||"0.00",this.commissionBalance=t.data.commissionBalance||"0.00",this.profitBalance=t.data.profitBalance||"0.00",this.inviteCode=t.data.shareCode,this.catBalance=t.data.catBalance||"0.00",this.avatarUrl=this.getAvatarUrl(t.data.avatar),this.isActivated=t.data.isActivated,this.commissionRate=t.data.commissionRate,a("userInfo",t.data),!t.data.status)return void s({url:"/pages/login/index"});this.isLeader=1===t.data.isLeader}}catch(e){console.error("获取用户信息失败:",e);const a=t("userInfo");a&&(this.userEmail=a.email||"",this.userNo=a.userNo||"",this.availableBalance=a.availableBalance||"0.00",this.copyTradeBalance=a.copyTradeBalance||"0.00",this.commissionBalance=a.commissionBalance||"0.00",this.profitBalance=a.profitBalance||"0.00",this.catBalance=a.catBalance||"0.00",this.avatarUrl=this.getAvatarUrl(a.avatar),this.isLeader=1===a.isLeader)}},async loadTodayStats(){this.statsLoading=!0;try{const a=await b({url:"/api/user/today-stats",method:"GET"});200===a.code&&a.data?(this.todayCommission=a.data.todayCommission,this.upCount=a.data.upCount,this.downCount=a.data.downCount):(this.todayCommission=0,this.upCount=0,this.downCount=0)}catch(a){this.todayCommission=0,this.upCount=0,this.downCount=0}finally{this.statsLoading=!1}},handleRecharge(){console.log("充值"),e({url:"/pages/recharge/index"})},handleTransfer(){this.showTransferPopup=!0},toggleFromDropdown(){this.showFromDropdown=!this.showFromDropdown,this.showToDropdown=!1},toggleToDropdown(){this.showToDropdown=!this.showToDropdown,this.showFromDropdown=!1},selectFrom(a){this.transferFrom=a,this.transferTo=null,this.showFromDropdown=!1},selectTo(a){this.transferTo=a,this.showToDropdown=!1},closeTransferPopup(){this.showTransferPopup=!1,this.transferAmount="",this.payPassword="",this.showFromDropdown=!1,this.showToDropdown=!1,this.transferFrom=null,this.transferTo=null},confirmTransfer(){if(null===this.transferFrom||null===this.transferTo)return void o({title:"请选择划转账户",icon:"none"});const a=Number(this.transferAmount);if(!this.transferAmount||""===this.transferAmount.trim())return void o({title:"请输入划转金额",icon:"none"});if(isNaN(a)||a<=0)return void o({title:"请输入有效金额（大于0）",icon:"none"});a>Number(this.getAccountBalance(this.transferFrom))?o({title:"余额不足",icon:"none"}):this.showPasswordPopup=!0},validateTransferRule:(a,s)=>"fund"===a&&"copy"===s||"copy"===a&&"fund"===s||("commission"===a&&("fund"===s||"copy"===s)||"profit"===a&&("fund"===s||"copy"===s)),closePasswordPopup(){this.showPasswordPopup=!1,this.payPassword=""},submitTransfer(){if(!this.payPassword)return void o({title:"请输入支付密码",icon:"none",duration:2e3,mask:!0});n({title:"划转中...",mask:!0});const a={fromAccountType:this.accountList[this.transferFrom].type,toAccountType:this.accountList[this.transferTo].type,amount:Number(this.transferAmount),payPassword:this.payPassword};b({url:"/api/user/transfer",method:"POST",data:a}).then((a=>{l(),200===a.code?(o({title:"划转成功",icon:"success",duration:2e3,mask:!0}),setTimeout((()=>{this.showPasswordPopup=!1,this.showTransferPopup=!1,this.transferAmount="",this.payPassword="",this.loadUserInfo()}),1500)):o({title:a.message||"划转失败",icon:"none",duration:2e3,mask:!0})})).catch((a=>{l(),console.error("划转失败:",a);let s="网络错误，请重试";a.response&&a.response.data&&a.response.data.message?s=a.response.data.message:a.message&&(s=a.message),o({title:s,icon:"none",duration:2e3,mask:!0})}))},handleTeam(){e({url:"/pages/team/index"})},handleInvite(){e({url:"/pages/share/index"})},goToAsset(){e({url:"/pages/asset/index"})},goToProfit(){e({url:"/pages/profit/index"})},handleWithdraw(){console.log("提现按钮被点击"),e({url:"/pages/withdraw/index"})},getAccountBalance(a){var s;const t=null==(s=this.accountList[a])?void 0:s.type;return"fund"===t?this.availableBalance:"commission"===t?this.commissionBalance:"copy"===t?this.copyTradeBalance:"profit"===t?this.profitBalance:"0.00"},goToUploadAvatar(){e({url:"/pages/mine/upload-avatar"})},goToLeaderCenter(){e({url:"/pages/leader/index"})}}},[["render",function(a,s,t,e,o,n){const l=g,k=v,b=r,B=C,P=T;return d(),i(b,{class:"container"},{default:c((()=>[u(b,{class:"user-info glass-effect animate-item"},{default:c((()=>[u(l,{class:"avatar",src:o.avatarUrl,onClick:n.goToUploadAvatar,onError:n.onAvatarError},null,8,["src","onClick","onError"]),u(b,{class:"user-details"},{default:c((()=>[u(k,{class:"account"},{default:c((()=>[p("账号："+m(n.maskEmail(o.userEmail)),1)])),_:1}),u(k,{class:"uid"},{default:c((()=>[p("UID："+m(o.userNo),1)])),_:1}),u(k,{class:"invite-code"},{default:c((()=>[p("CAT："+m(o.catBalance),1)])),_:1}),o.commissionRate>0&&o.isActivated?(d(),i(k,{key:0,class:"invite-code"},{default:c((()=>[p("佣金比例："+m(o.commissionRate)+"%",1)])),_:1})):f("",!0)])),_:1})])),_:1}),u(b,{class:"stats-card glass-effect animate-item"},{default:c((()=>[u(b,{class:"stats-btn-row"},{default:c((()=>[u(B,{class:"recharge-btn",onClick:n.handleRecharge},{default:c((()=>[p("充值")])),_:1},8,["onClick"]),u(B,{class:"withdraw-btn",style:{"z-index":"999"},onClick:n.handleWithdraw},{default:c((()=>[p("提现")])),_:1},8,["onClick"]),u(B,{class:"transfer-btn",onClick:n.handleTransfer},{default:c((()=>[p("划转")])),_:1},8,["onClick"])])),_:1}),u(b,{class:"account-grid"},{default:c((()=>[u(b,{class:"account-row"},{default:c((()=>[u(b,{class:"account-card",onClick:n.goToAsset},{default:c((()=>[u(k,{class:"account-name"},{default:c((()=>[p("资金账户(USDT)")])),_:1}),u(k,{class:"account-balance"},{default:c((()=>[p(m(o.availableBalance),1)])),_:1})])),_:1},8,["onClick"]),u(b,{class:"account-card"},{default:c((()=>[u(k,{class:"account-name"},{default:c((()=>[p("跟单账户(USDT)")])),_:1}),u(k,{class:"account-balance"},{default:c((()=>[p(m(o.copyTradeBalance),1)])),_:1})])),_:1})])),_:1}),u(b,{class:"account-row"},{default:c((()=>[u(b,{class:"account-card"},{default:c((()=>[u(k,{class:"account-name"},{default:c((()=>[p("佣金账户(USDT)")])),_:1}),u(k,{class:"account-balance"},{default:c((()=>[p(m(o.commissionBalance),1)])),_:1})])),_:1}),u(b,{class:"account-card"},{default:c((()=>[u(k,{class:"account-name"},{default:c((()=>[p("利润账户(USDT)")])),_:1}),u(k,{class:"account-balance"},{default:c((()=>[p(m(o.profitBalance),1)])),_:1})])),_:1})])),_:1})])),_:1}),u(b,{class:"stats-summary"},{default:c((()=>[u(b,{class:"summary-item main"},{default:c((()=>[u(k,{class:"summary-label"},{default:c((()=>[p("今日收益(USDT)")])),_:1}),u(k,{class:"summary-value"},{default:c((()=>[p(m(o.statsLoading?"--":+o.todayCommission),1)])),_:1})])),_:1}),u(b,{class:"summary-item"},{default:c((()=>[u(k,{class:"summary-label"},{default:c((()=>[p("买涨(次)")])),_:1}),u(k,{class:"summary-value"},{default:c((()=>[p(m(o.statsLoading?"--":o.upCount),1)])),_:1})])),_:1}),u(b,{class:"summary-item"},{default:c((()=>[u(k,{class:"summary-label"},{default:c((()=>[p("买跌(次)")])),_:1}),u(k,{class:"summary-value"},{default:c((()=>[p(m(o.statsLoading?"--":o.downCount),1)])),_:1})])),_:1})])),_:1})])),_:1}),u(b,{class:"asset-extra-card"},{default:c((()=>[u(b,{class:"extra-item team",onClick:n.handleTeam},{default:c((()=>[u(b,{class:"extra-icon team-icon"},{default:c((()=>[u(l,{src:"/assets/team-DflAYIix.png",mode:"aspectFit"})])),_:1}),u(k,{class:"extra-label"},{default:c((()=>[p("我的团队")])),_:1})])),_:1},8,["onClick"]),u(b,{class:"extra-item profit",onClick:n.goToProfit},{default:c((()=>[u(b,{class:"extra-icon profit-icon"},{default:c((()=>[u(l,{src:"/assets/shouyi-t2um24fh.png",mode:"aspectFit"})])),_:1}),u(b,{class:"extra-info"},{default:c((()=>[u(k,{class:"extra-label"},{default:c((()=>[p("佣金/收益明细")])),_:1})])),_:1})])),_:1},8,["onClick"])])),_:1}),o.isLeader?(d(),i(b,{key:0,class:"leader-center-card",onClick:n.goToLeaderCenter},{default:c((()=>[u(b,{class:"leader-content"},{default:c((()=>[u(b,{class:"leader-icon-wrapper"},{default:c((()=>[u(l,{src:"/assets/daidan-CpNW83Bs.png",mode:"aspectFit",class:"leader-icon"})])),_:1}),u(b,{class:"leader-info"},{default:c((()=>[u(k,{class:"leader-title"},{default:c((()=>[p("带单员中心")])),_:1}),u(k,{class:"leader-desc"},{default:c((()=>[p("管理您的带单业务")])),_:1})])),_:1}),u(l,{src:"/assets/gold-D0bJBoCs.png",mode:"aspectFit",class:"leader-gold-img"})])),_:1})])),_:1},8,["onClick"])):f("",!0),o.isLeader?(d(),i(b,{key:1,class:"section-divider"})):f("",!0),u(b,{class:"invite-banner",onClick:n.handleInvite},{default:c((()=>[u(b,{class:"invite-content"},{default:c((()=>[u(b,{class:"invite-icon-wrapper"},{default:c((()=>[u(l,{src:"/assets/yaoqing-jVDFoiCd.png",mode:"aspectFit",class:"invite-icon"})])),_:1}),u(b,{class:"invite-text"},{default:c((()=>[u(k,{class:"invite-title"},{default:c((()=>[p("人人有礼")])),_:1}),u(k,{class:"invite-desc"},{default:c((()=>[p("邀请好友得奖励")])),_:1})])),_:1})])),_:1}),u(l,{class:"invite-img",src:"/assets/lipin-DbXBzme3.png",mode:"aspectFit"})])),_:1},8,["onClick"]),o.showTransferPopup?(d(),i(b,{key:2,class:"transfer-popup"},{default:c((()=>[u(b,{class:"popup-content",onClick:s[1]||(s[1]=_((()=>{}),["stop"]))},{default:c((()=>[u(b,{class:"popup-title"},{default:c((()=>[p("账户划转")])),_:1}),u(b,{class:"custom-select",onClick:n.toggleFromDropdown},{default:c((()=>[u(b,{class:"picker-label"},{default:c((()=>[p("从账户")])),_:1}),u(b,{class:"picker-value"},{default:c((()=>{var a;return[p(m((null==(a=o.accountList[o.transferFrom])?void 0:a.name)||"请选择"),1)]})),_:1}),u(b,{class:"arrow"},{default:c((()=>[p("▼")])),_:1}),o.showFromDropdown?(d(),i(b,{key:0,class:"dropdown-list"},{default:c((()=>[(d(!0),h(y,null,w(o.accountList,((a,s)=>(d(),i(b,{class:"dropdown-item",key:s,onClick:_((a=>n.selectFrom(s)),["stop"])},{default:c((()=>[p(m(a.name),1)])),_:2},1032,["onClick"])))),128))])),_:1})):f("",!0)])),_:1},8,["onClick"]),u(b,{class:"custom-select",onClick:n.toggleToDropdown},{default:c((()=>[u(b,{class:"picker-label"},{default:c((()=>[p("到账户")])),_:1}),u(b,{class:"picker-value"},{default:c((()=>{var a;return[p(m((null==(a=o.accountList[o.transferTo])?void 0:a.name)||"请选择"),1)]})),_:1}),u(b,{class:"arrow"},{default:c((()=>[p("▼")])),_:1}),o.showToDropdown?(d(),i(b,{key:0,class:"dropdown-list"},{default:c((()=>[(d(!0),h(y,null,w(n.availableToAccounts,((a,s)=>(d(),i(b,{class:"dropdown-item",key:s,onClick:_((s=>n.selectTo(o.accountList.findIndex((s=>s.type===a.type)))),["stop"])},{default:c((()=>[p(m(a.name),1)])),_:2},1032,["onClick"])))),128))])),_:1})):f("",!0)])),_:1},8,["onClick"]),u(b,{class:"balance-row"},{default:c((()=>[p("余额："+m(n.getAccountBalance(o.transferFrom))+" USDT",1)])),_:1}),u(b,{class:"input-row"},{default:c((()=>[u(P,{class:"amount-input",modelValue:o.transferAmount,"onUpdate:modelValue":s[0]||(s[0]=a=>o.transferAmount=a),type:"number",placeholder:"请输入划转金额"},null,8,["modelValue"])])),_:1}),u(B,{class:"confirm-btn",onClick:n.confirmTransfer},{default:c((()=>[p("确定")])),_:1},8,["onClick"]),u(b,{class:"close-btn",onClick:n.closeTransferPopup},{default:c((()=>[p("×")])),_:1},8,["onClick"])])),_:1})])),_:1})):f("",!0),o.showPasswordPopup?(d(),i(b,{key:3,class:"password-popup",onClick:n.closePasswordPopup},{default:c((()=>[u(b,{class:"popup-content",onClick:s[3]||(s[3]=_((()=>{}),["stop"]))},{default:c((()=>[u(b,{class:"popup-title"},{default:c((()=>[p("请输入支付密码")])),_:1}),u(P,{class:"password-input",modelValue:o.payPassword,"onUpdate:modelValue":s[2]||(s[2]=a=>o.payPassword=a),type:"password",placeholder:"支付密码"},null,8,["modelValue"]),u(B,{class:"confirm-btn",onClick:n.submitTransfer},{default:c((()=>[p("确认划转")])),_:1},8,["onClick"]),u(b,{class:"close-btn",onClick:n.closePasswordPopup},{default:c((()=>[p("×")])),_:1},8,["onClick"])])),_:1})])),_:1},8,["onClick"])):f("",!0)])),_:1})}],["__scopeId","data-v-bc319449"]]);export{A as default};
