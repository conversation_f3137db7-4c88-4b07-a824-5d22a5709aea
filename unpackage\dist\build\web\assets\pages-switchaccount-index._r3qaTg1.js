import{H as e,n as t,g as c,b as a,s,r as o,e as n,d as i,f as r,h as l,w as u,i as d,o as f,j as p,m as h,z as m,l as y,q as g,F as k,x as v,y as _,p as b,t as x,u as A,v as w}from"./index-DSmyjKbQ.js";import{_ as C}from"./uni-icons.ouCj1myJ.js";import{r as N}from"./uni-app.es.CZvmdujn.js";import{_ as z}from"./_plugin-vue_export-helper.BCo6x5W8.js";const B=z({data:()=>({statusBarHeight:0,accountList:[],currentAccount:null}),created(){const t=e();this.statusBarHeight=t.statusBarHeight},onShow(){this.loadAccounts()},methods:{handleBack(){t()},loadAccounts(){const e=c("userInfo");this.currentAccount=e;const t=c("savedAccounts")||[];!t.find((t=>t.phone===e.phone))&&e.phone&&(t.unshift({...e,token:c("token")}),a("savedAccounts",t)),this.accountList=t},formatPhone:e=>e?e.substr(0,3)+"****"+e.substr(-4):"",formatUserType:e=>0===e?"普通会员":1===e?"合伙人":2===e?"联创":"普通会员",async switchAccount(e){if(e.phone!==this.currentAccount.phone)try{a("token",e.token),a("userInfo",e),s({title:"切换成功",icon:"success",duration:1500}),setTimeout((()=>{o({url:"/pages/index/index"})}),1500)}catch(t){s({title:"切换失败",icon:"none",duration:1500})}},addNewAccount(){a("loginMode","addAccount"),a("tempUserInfo",this.currentAccount),a("tempToken",c("token")),n({url:"/pages/login/index"})},confirmDelete(e){i({title:"提示",content:"确定要移除该账号吗？",success:t=>{t.confirm&&this.removeAccount(e)}})},removeAccount(e){let t=c("savedAccounts")||[];t=t.filter((t=>t.phone!==e.phone)),a("savedAccounts",t),this.accountList=t,s({title:"已移除该账号",icon:"success"})}}},[["render",function(e,t,c,a,s,o){const n=N(r("uni-icons"),C),i=d,z=v,B=w,T=_;return f(),l(i,{class:"switch-account-container"},{default:u((()=>[p(i,{class:"custom-navbar",style:m({paddingTop:s.statusBarHeight+"px"})},{default:u((()=>[p(i,{class:"navbar-content"},{default:u((()=>[p(i,{class:"left-area",onClick:o.handleBack},{default:u((()=>[p(n,{type:"left",size:"20",color:"#fff"})])),_:1},8,["onClick"]),p(z,{class:"page-title"},{default:u((()=>[h("切换账号")])),_:1}),p(i,{class:"right-area"})])),_:1})])),_:1},8,["style"]),p(i,{class:"content-box"},{default:u((()=>[p(i,{class:"account-list"},{default:u((()=>[(f(!0),y(k,null,g(s.accountList,((e,t)=>(f(),l(i,{class:b(["account-item",{current:e.phone===s.currentAccount.phone}]),key:t,onClick:t=>o.switchAccount(e)},{default:u((()=>[p(i,{class:"account-info"},{default:u((()=>[p(B,{class:"avatar",src:"data:image/svg+xml,%3csvg%20width='120'%20height='120'%20viewBox='0%200%20120%20120'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3c!--%20背景圆圈%20--%3e%3ccircle%20cx='60'%20cy='60'%20r='60'%20fill='rgba(34,%20209,%20238,%200.1)'%3e%3canimate%20attributeName='opacity'%20values='0.1;0.15;0.1'%20dur='3s'%20repeatCount='indefinite'/%3e%3c/circle%3e%3c!--%20外圈装饰%20--%3e%3ccircle%20cx='60'%20cy='60'%20r='56'%20stroke='%2322d1ee'%20stroke-width='2'%20opacity='0.3'%3e%3canimate%20attributeName='stroke-dasharray'%20values='0%20360;360%20360'%20dur='2s'%20fill='freeze'/%3e%3canimateTransform%20attributeName='transform'%20type='rotate'%20from='0%2060%2060'%20to='360%2060%2060'%20dur='20s'%20repeatCount='indefinite'/%3e%3c/circle%3e%3c!--%20头像主体%20--%3e%3cg%20transform='translate(30,%2025)'%3e%3c!--%20头部轮廓%20--%3e%3ccircle%20cx='30'%20cy='30'%20r='24'%20fill='%2322d1ee'%20opacity='0.2'/%3e%3c!--%20身体轮廓%20--%3e%3cpath%20d='M0%2065c0-16.6%2013.4-30%2030-30s30%2013.4%2030%2030'%20stroke='%2322d1ee'%20stroke-width='2'%20opacity='0.3'%3e%3canimate%20attributeName='stroke-dasharray'%20values='0%20100;100%20100'%20dur='1s'%20fill='freeze'/%3e%3c/path%3e%3c!--%20动态波纹效果%20--%3e%3ccircle%20cx='30'%20cy='30'%20r='28'%20stroke='%2322d1ee'%20stroke-width='2'%20opacity='0.1'%3e%3canimate%20attributeName='r'%20values='28;32;28'%20dur='3s'%20repeatCount='indefinite'/%3e%3canimate%20attributeName='opacity'%20values='0.1;0.2;0.1'%20dur='3s'%20repeatCount='indefinite'/%3e%3c/circle%3e%3c/g%3e%3c!--%20装饰点%20--%3e%3ccircle%20cx='85'%20cy='35'%20r='4'%20fill='%2322d1ee'%20opacity='0.5'%3e%3canimate%20attributeName='opacity'%20values='0.5;0.8;0.5'%20dur='2s'%20repeatCount='indefinite'/%3e%3c/circle%3e%3ccircle%20cx='35'%20cy='85'%20r='4'%20fill='%2322d1ee'%20opacity='0.5'%3e%3canimate%20attributeName='opacity'%20values='0.5;0.8;0.5'%20dur='2s'%20begin='1s'%20repeatCount='indefinite'/%3e%3c/circle%3e%3c!--%20悬停效果%20--%3e%3ccircle%20cx='60'%20cy='60'%20r='58'%20stroke='url(%23avatar-gradient)'%20stroke-width='2'%20opacity='0'%3e%3canimate%20attributeName='opacity'%20values='0;0.6;0'%20dur='2s'%20begin='mouseover'%20fill='freeze'/%3e%3c/circle%3e%3c!--%20渐变定义%20--%3e%3cdefs%3e%3clinearGradient%20id='avatar-gradient'%20x1='0%25'%20y1='0%25'%20x2='100%25'%20y2='100%25'%3e%3cstop%20offset='0%25'%20style='stop-color:%2322d1ee;stop-opacity:0.5'/%3e%3cstop%20offset='100%25'%20style='stop-color:%2322d1ee;stop-opacity:0'/%3e%3c/linearGradient%3e%3c/defs%3e%3c/svg%3e"}),p(i,{class:"account-details"},{default:u((()=>[p(z,{class:"phone"},{default:u((()=>[h(x(o.formatPhone(e.phone)),1)])),_:2},1024),p(z,{class:"type"},{default:u((()=>[h(x(o.formatUserType(e.isManager)),1)])),_:2},1024)])),_:2},1024)])),_:2},1024),p(i,{class:"right-actions"},{default:u((()=>[e.phone===s.currentAccount.phone?(f(),y(k,{key:0},[p(i,{class:"active-dot"}),p(z,{class:"current-tag"},{default:u((()=>[h("使用中")])),_:1})],64)):(f(),y(k,{key:1},[p(z,{class:"delete-btn",onClick:A((t=>o.confirmDelete(e)),["stop"])},{default:u((()=>[h("移除")])),_:2},1032,["onClick"]),p(n,{type:"right",size:"16",color:"rgba(255, 255, 255, 0.6)"})],64))])),_:2},1024)])),_:2},1032,["class","onClick"])))),128))])),_:1}),p(T,{class:"add-account-btn",onClick:o.addNewAccount},{default:u((()=>[p(n,{type:"plus",size:"20",color:"#fff"}),p(z,null,{default:u((()=>[h("添加新账号")])),_:1})])),_:1},8,["onClick"])])),_:1})])),_:1})}],["__scopeId","data-v-a03032c3"]]);export{B as default};
