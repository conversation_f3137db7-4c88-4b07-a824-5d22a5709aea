<!DOCTYPE html>
<html lang="en">
  <head>
    <link rel="stylesheet" href="/assets/uni.9acf7ef5.css">

    <meta charset="UTF-8" />
    <script>
      var coverSupport = 'CSS' in window && typeof CSS.supports === 'function' && (CSS.supports('top: env(a)') ||
        CSS.supports('top: constant(a)'))
      document.write(
        '<meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0' +
        (coverSupport ? ', viewport-fit=cover' : '') + '" />')
    </script>
    <title>卡塔</title>
    <!--preload-links-->
    <!--app-context-->
    <script type="module" crossorigin src="/assets/index-DSmyjKbQ.js"></script>
    <link rel="stylesheet" crossorigin href="/assets/index-Df6Jn914.css">
  </head>
  <body>
    <div id="app"><!--app-html--></div>
  </body>
</html>
