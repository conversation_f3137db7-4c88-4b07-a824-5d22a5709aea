import{H as a,n as t,f as e,h as s,w as i,z as r,i as o,o as l,j as d,m as n,p as c,l as g,q as h,F as p,k as u,x as f,t as m}from"./index-DSmyjKbQ.js";import{_}from"./uni-icons.ouCj1myJ.js";import{r as T}from"./uni-app.es.CZvmdujn.js";import{r as v}from"./request.DeqggKcp.js";import{_ as y}from"./_plugin-vue_export-helper.BCo6x5W8.js";import"./index.B6QF5Ba_.js";const b=y({data:()=>({statusBarHeight:0,navBarHeight:44,navPaddingTop:44,activeTab:0,recordList:[],loading:!1,total:0,page:1,pageSize:10}),created(){const t=a();this.statusBarHeight=t.statusBarHeight,this.navPaddingTop=this.statusBarHeight+this.navBarHeight,this.getRecordList(!0)},methods:{handleBack(){t()},onTabChange(a){this.activeTab=a,this.getRecordList(!0)},async getRecordList(a=!1){this.loading=!0,a&&(this.page=1);const t=[2,1][this.activeTab];try{const e=await v({url:"/api/transfer/list",method:"GET",data:{page:this.page,pageSize:this.pageSize,type:t}});200===e.code&&e.data&&(this.recordList=a?e.data.records:this.recordList.concat(e.data.records),this.total=e.data.total)}finally{this.loading=!1}},loadMore(){this.recordList.length<this.total&&!this.loading&&(this.page++,this.getRecordList())},formatTime(a){if(!a)return"";const t=new Date(a);return`${t.getFullYear()}-${(t.getMonth()+1).toString().padStart(2,"0")}-${t.getDate().toString().padStart(2,"0")} ${t.getHours().toString().padStart(2,"0")}:${t.getMinutes().toString().padStart(2,"0")}`}}},[["render",function(a,t,v,y,b,k){const S=T(e("uni-icons"),_),L=o,x=f;return l(),s(L,{class:"record-container",style:r({paddingTop:b.navPaddingTop+"px"})},{default:i((()=>[d(L,{class:"custom-navbar",style:r({paddingTop:b.statusBarHeight+"px"})},{default:i((()=>[d(L,{class:"navbar-content"},{default:i((()=>[d(L,{class:"left-area",onClick:k.handleBack},{default:i((()=>[d(S,{type:"left",size:"20",color:"#fff"})])),_:1},8,["onClick"]),d(x,{class:"page-title"},{default:i((()=>[n("互转记录")])),_:1}),d(L,{class:"right-area"})])),_:1})])),_:1},8,["style"]),d(L,{class:"tab-bar"},{default:i((()=>[d(L,{class:c(["tab",{active:0===b.activeTab}]),onClick:t[0]||(t[0]=a=>k.onTabChange(0))},{default:i((()=>[n("转出记录")])),_:1},8,["class"]),d(L,{class:c(["tab",{active:1===b.activeTab}]),onClick:t[1]||(t[1]=a=>k.onTabChange(1))},{default:i((()=>[n("转入记录")])),_:1},8,["class"])])),_:1}),d(L,{class:"record-card"},{default:i((()=>[(l(!0),g(p,null,h(b.recordList,((a,t)=>(l(),s(L,{key:a.id,class:"record-item"},{default:i((()=>[d(x,{class:"record-username"},{default:i((()=>[n(m(a.toUsername),1)])),_:2},1024),d(x,{class:"record-commission"},{default:i((()=>[n("-"+m(a.amount)+"USDT",1)])),_:2},1024),d(x,{class:"record-time",style:{"margin-left":"32rpx"}},{default:i((()=>[n(m(k.formatTime(a.createTime)),1)])),_:2},1024)])),_:2},1024)))),128)),b.loading?(l(),s(L,{key:0,class:"loading-text"},{default:i((()=>[n("加载中...")])),_:1})):u("",!0),b.loading||0!==b.recordList.length?u("",!0):(l(),s(L,{key:1,class:"empty-text"},{default:i((()=>[n("暂无数据")])),_:1})),!b.loading&&b.recordList.length<b.total?(l(),s(L,{key:2,class:"load-more",onClick:k.loadMore},{default:i((()=>[n("加载更多")])),_:1},8,["onClick"])):u("",!0)])),_:1})])),_:1},8,["style"])}],["__scopeId","data-v-d377431a"]]);export{b as default};
