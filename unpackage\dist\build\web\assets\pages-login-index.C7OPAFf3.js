import{g as o,s as e,r as t,a as n,b as a,n as l,c as i,d as s,e as c,f as r,h as d,w as u,i as h,o as g,j as p,k as m,l as f,m as k,t as w,p as C,F as y,q as L,u as T,v as _,x as b,I as v,y as x}from"./index-9TNq14KG.js";import{_ as S}from"./uni-icons.Dtv1TTQG.js";import{r as F}from"./uni-app.es.Df5Q6fJy.js";import{_ as M}from"./uni-popup.C8Rx2OQb.js";import{r as P}from"./request.B3nfocmy.js";import{t as A}from"./i18n.DFw1mk0W.js";import{_ as I}from"./_plugin-vue_export-helper.BCo6x5W8.js";import"./index.B6QF5Ba_.js";const D=I({data:()=>({showPhoneLogin:!1,loginType:"email",isLoading:!0,isSubmitting:!1,isDownloading:!1,showCustomModal:!1,showAutoLoginLoading:!1,fakeForm:{a:"",b:""},countdown:60,isCountingDown:!1,timer:null,contractContent:"",hasReadToBottom:!1,scrollTop:0,lastScrollTop:0,isScrolledToBottom:!1,shareCode:"",loginMode:"normal",pageTitle:A("login"),showLanguagePopup:!1,langList:[{label:"简体中文",value:"zh-CN"},{label:"繁体中文",value:"zh-TW"},{label:"English",value:"en"}],langSelected:"zh-CN"}),async onLoad(){this.isLoading=!0;try{if(this.loginMode=o("loginMode")||"normal",this.pageTitle="normal"===this.loginMode?A("login"):"addAccount"===this.loginMode?A("addAccount"):A("relogin"),this.checkSimpleAutoLogin())return;if(await this.checkAutoLogin())return;if("switchAccount"===this.loginMode){const e=o("targetPhone");e&&(this.fakeForm.a=e)}}catch(e){console.error("onLoad error:",e)}finally{this.isLoading=!1}},onShow(){this.clearForm()},methods:{t:A,async checkAutoLogin(){try{const a=o("token"),l=o("userInfo");if(a&&l)if(console.log("发现缓存的用户信息，尝试自动登录..."),this.shareCode=l.shareCode,l.status)try{if((await P({url:"/api/auth/check-phone",method:"Get",params:{phone:l.phone||l.email}})).data)return console.log("自动登录成功，跳转到首页"),e({title:A("autoLoginSuccess")||"自动登录成功",icon:"success",duration:1500}),setTimeout((()=>{t({url:"/pages/index/index"})}),1500),!0;console.log("API返回token已失效，清除缓存"),this.clearUserCache()}catch(n){console.log("自动登录验证请求失败:",n),401===n.statusCode||403===n.statusCode?(console.log("认证失败，清除缓存"),this.clearUserCache()):console.log("网络或其他错误，保留缓存，跳过自动登录")}else console.log("账户已被锁定，清除缓存"),this.clearUserCache(),e({title:A("accountLocked"),icon:"none"});else console.log("未发现缓存的用户信息")}catch(n){console.error("自动登录检查过程出错:",n),console.log("保留缓存，跳过自动登录")}return!1},checkSimpleAutoLogin(){try{const e=o("token"),n=o("userInfo");if(e&&n&&n.status)return console.log("发现有效缓存，直接自动登录"),this.showAutoLoginLoading=!0,setTimeout((()=>{t({url:"/pages/index/index"})}),1e3),!0}catch(e){console.log("简单自动登录检查失败:",e)}return!1},clearUserCache(){n("token"),n("userInfo"),console.log("已清除用户缓存")},clearForm(){this.fakeForm={a:"",b:""}},async sendCode(){if(this.fakeForm.a)if(/^1[3-9]\d{9}$/.test(this.fakeForm.a))try{await P({url:"/api/auth/send-code-login",method:"POST",params:{phone:this.fakeForm.a}}),this.startCountDown(),e({title:A("sendSuccess"),icon:"none",duration:1500})}catch(o){console.error("发送验证码失败:",o),e({title:o.message||A("sendFail"),icon:"none",duration:1500})}else e({title:A("inputPhoneValid"),icon:"none",duration:1500});else e({title:A("inputPhone"),icon:"none",duration:1500})},startCountDown(){this.isCountingDown=!0,this.countdown=60,this.timer=setInterval((()=>{this.countdown>0?this.countdown--:(this.stopCountDown(),this.isCountingDown=!1)}),1e3)},stopCountDown(){clearInterval(this.timer),this.timer=null,this.isCountingDown=!1,this.countdown=60},async handleLogin(){if("phone"===this.loginType){if(!this.fakeForm.a)return void e({title:A("inputPhone"),icon:"none",duration:1500});if(!/^1[3-9]\d{9}$/.test(this.fakeForm.a))return void e({title:A("inputPhoneValid"),icon:"none",duration:1500})}else{if(!this.fakeForm.a)return void e({title:A("inputEmail"),icon:"none",duration:1500});if(!/^\w+([-+.']\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/.test(this.fakeForm.a))return void e({title:A("inputEmailValid"),icon:"none",duration:1500})}if(!this.fakeForm.b)return void e({title:A("inputPassword"),icon:"none",duration:1500});let o={password:this.fakeForm.b};"phone"===this.loginType?o.phone=this.fakeForm.a:o.email=this.fakeForm.a,this.isSubmitting=!0;try{let e=await this.performLogin(o);e.data&&("addAccount"===this.loginMode||"switchAccount"===this.loginMode?await this.handleAccountOperation(e.data):await this.handleNormalLogin(e.data))}catch(t){this.handleLoginError(t)}finally{this.isSubmitting=!1}},handleContractScroll(o){const{scrollHeight:e,scrollTop:t,clientHeight:n}=o.detail;this.lastScrollTop=t,e-(t+n)<20&&(this.hasReadToBottom=!0,this.isScrolledToBottom=!0)},handleScrollToLower(){this.hasReadToBottom=!0,this.isScrolledToBottom=!0},handleCancel(){const e=o("tempUserInfo"),t=o("tempToken");e&&t&&(a("userInfo",e),a("token",t)),n("tempUserInfo"),n("tempToken"),n("loginMode"),n("targetPhone"),l()},performLogin:async o=>await P({url:"/api/auth/login",method:"POST",data:o}),async handleAccountOperation(n){const i=o("savedAccounts")||[],s=i.findIndex((o=>o.phone===n.userInfo.phone)),c={...n.userInfo,token:n.token};-1!==s?i[s]=c:i.unshift(c),a("savedAccounts",i),"switchAccount"===this.loginMode?(a("userInfo",n.userInfo),a("token",n.token),this.clearLoginModeAndTemp(),e({title:A("switchSuccess"),icon:"success",duration:1500}),setTimeout((()=>{t({url:"/pages/index/index"})}),1500)):"addAccount"===this.loginMode&&(a("userInfo",n.userInfo),a("token",n.token),this.clearLoginModeAndTemp(),e({title:A("addSuccess"),icon:"success",duration:1500}),setTimeout((()=>{l()}),1500))},async handleNormalLogin(o){a("token",o.token),a("userInfo",o.userInfo),0===o.userInfo.contractAgreement||(e({title:A("loginSuccess"),icon:"none",duration:1500}),setTimeout((()=>{i({url:"/pages/index/index"})}),1500))},handleLoginError(o){console.error(`${"normal"===this.loginMode?A("login"):A("operate")}${A("fail")}:`,o),e({title:o.message||`${"normal"===this.loginMode?A("login"):A("operate")}${A("fail")}`,icon:"none",duration:1500})},clearLoginModeAndTemp(){n("loginMode"),n("targetPhone"),n("tempUserInfo"),n("tempToken")},handleDownload(){if(this.isDownloading)return;this.isDownloading=!0;this.copyDownloadLink("https://downloade.catcoinvip.com/download-apk"),setTimeout((()=>{this.isDownloading=!1}),1e3)},copyDownloadLink(o){try{navigator.clipboard?navigator.clipboard.writeText(o).then((()=>{e({title:"复制下载地址成功",icon:"none",duration:2e3}),setTimeout((()=>{this.showCustomModal=!0}),2200)})).catch((()=>{this.fallbackCopy(o)})):this.fallbackCopy(o)}catch(t){console.log("复制链接失败:",t),this.fallbackCopy(o)}},fallbackCopy(o){try{const t=document.createElement("textarea");t.value=o,t.style.position="fixed",t.style.opacity="0",document.body.appendChild(t),t.select(),document.execCommand("copy"),document.body.removeChild(t),e({title:"复制下载地址成功",icon:"none",duration:2e3}),setTimeout((()=>{this.showCustomModal=!0}),2200)}catch(t){s({title:"下载地址",content:`请复制此链接到浏览器下载：${o}`,showCancel:!0,cancelText:"取消",confirmText:"复制",success:e=>{if(e.confirm)try{navigator.clipboard&&navigator.clipboard.writeText(o)}catch(t){console.log("二次复制失败:",t)}}})}},closeCustomModal(){this.showCustomModal=!1},goToRegister(){c({url:"/pages/register/index"})},goToForget(){c({url:"/pages/forget/index"})},goToReset(){console.log("goToReset"),c({url:"/pages/reset/index"})},selectLang(o){this.langSelected=o},onLangCancel(){this.$refs.langPopup.close()},onLangConfirm(){a("langSelected",this.langSelected),this.$refs.langPopup.close(),setTimeout((()=>{t({url:"/pages/login/index"})}),200)},openLangPopup(){this.$refs.langPopup.open("bottom")}},mounted(){const e=o("langSelected");e&&(this.langSelected=e)},beforeDestroy(){this.timer&&clearInterval(this.timer)}},[["render",function(o,e,t,n,a,l){const i=h,s=_,c=b,P=F(r("uni-icons"),S),A=v,I=x,D=F(r("uni-popup"),M);return g(),d(i,{class:"login-container"},{default:u((()=>[a.isLoading?(g(),d(i,{key:0,class:"loading-overlay"},{default:u((()=>[p(i,{class:"loading-spinner"})])),_:1})):m("",!0),a.isLoading?m("",!0):(g(),f(y,{key:1},[p(i,{class:"header"},{default:u((()=>[p(s,{src:"/assets/logo-D-Pajj9X.png",class:"login-logo",mode:"widthFix"}),p(i,{class:"welcome-main"},{default:u((()=>[p(c,{class:"welcome-sub"},{default:u((()=>[k(w(l.t("welcome")),1)])),_:1})])),_:1})])),_:1}),p(i,{class:"form-box"},{default:u((()=>[a.showPhoneLogin?(g(),d(i,{key:0,class:"login-type-tabs"},{default:u((()=>[p(i,{class:C(["tab","phone"===a.loginType?"active":""]),onClick:e[0]||(e[0]=o=>a.loginType="phone")},{default:u((()=>[k(w(l.t("phoneLogin")),1)])),_:1},8,["class"]),p(i,{class:C(["tab","email"===a.loginType?"active":""]),onClick:e[1]||(e[1]=o=>a.loginType="email")},{default:u((()=>[k(w(l.t("emailLogin")),1)])),_:1},8,["class"])])),_:1})):m("",!0),a.showPhoneLogin&&"phone"===a.loginType?(g(),d(i,{key:1,class:"input-item"},{default:u((()=>[p(P,{type:"phone",size:"20",color:"var(--theme-accent-color)"}),p(A,{type:"text",name:"not-phone",placeholder:l.t("phoneLogin"),modelValue:a.fakeForm.a,"onUpdate:modelValue":e[2]||(e[2]=o=>a.fakeForm.a=o),autocomplete:"off",autocapitalize:"off",autocorrect:"off",spellcheck:"false"},null,8,["placeholder","modelValue"])])),_:1})):m("",!0),a.showPhoneLogin&&"email"!==a.loginType?m("",!0):(g(),d(i,{key:2,class:"input-item"},{default:u((()=>[p(P,{type:"email",size:"20",color:"var(--theme-accent-color)"}),p(A,{type:"text",name:"not-email",placeholder:l.t("emailLogin"),modelValue:a.fakeForm.a,"onUpdate:modelValue":e[3]||(e[3]=o=>a.fakeForm.a=o),autocomplete:"off",autocapitalize:"off",autocorrect:"off",spellcheck:"false"},null,8,["placeholder","modelValue"])])),_:1})),p(i,{class:"input-item"},{default:u((()=>[p(P,{type:"locked",size:"20",color:"#fff"}),p(A,{type:"password",name:"not-password",placeholder:l.t("password"),modelValue:a.fakeForm.b,"onUpdate:modelValue":e[4]||(e[4]=o=>a.fakeForm.b=o),autocomplete:"off",autocapitalize:"off",autocorrect:"off",spellcheck:"false"},null,8,["placeholder","modelValue"])])),_:1}),p(i,{class:"action-row"},{default:u((()=>[p(i,{class:"forget-link",onClick:l.goToReset},{default:u((()=>[p(c,null,{default:u((()=>[k(w(l.t("forgotPassword")),1)])),_:1})])),_:1},8,["onClick"]),p(i,{class:"register-link",onClick:l.goToRegister},{default:u((()=>[p(c,null,{default:u((()=>[k(w(l.t("register")),1)])),_:1})])),_:1},8,["onClick"])])),_:1}),p(I,{class:"login-btn",onClick:l.handleLogin,disabled:a.isSubmitting},{default:u((()=>[k(w(a.isSubmitting?l.t("loginLoading"):l.t("login")),1)])),_:1},8,["onClick","disabled"]),p(I,{class:"download-btn",onClick:l.handleDownload},{default:u((()=>[k(w(l.t("download")),1)])),_:1},8,["onClick"]),"normal"!==a.loginMode?(g(),d(i,{key:3,class:"cancel-link",onClick:l.handleCancel},{default:u((()=>[p(c,null,{default:u((()=>[k(w(l.t("cancel")),1)])),_:1})])),_:1},8,["onClick"])):m("",!0)])),_:1})],64)),p(D,{ref:"langPopup",type:"bottom",modelValue:a.showLanguagePopup,"onUpdate:modelValue":e[5]||(e[5]=o=>a.showLanguagePopup=o)},{default:u((()=>[p(i,{class:"lang-popup-box"},{default:u((()=>[p(i,{class:"lang-popup-title"},{default:u((()=>[k(w(l.t("langSetting")),1)])),_:1}),p(i,{class:"lang-options"},{default:u((()=>[(g(!0),f(y,null,L(a.langList,((o,e)=>(g(),d(i,{key:o.value,class:C(["lang-option",{active:a.langSelected===o.value}]),onClick:e=>l.selectLang(o.value)},{default:u((()=>[p(c,null,{default:u((()=>[k(w(o.label),1)])),_:2},1024)])),_:2},1032,["class","onClick"])))),128))])),_:1}),p(i,{class:"lang-popup-actions"},{default:u((()=>[p(I,{class:"lang-cancel",onClick:l.onLangCancel},{default:u((()=>[k(w(l.t("cancel")),1)])),_:1},8,["onClick"]),p(I,{class:"lang-confirm",onClick:l.onLangConfirm},{default:u((()=>[k(w(l.t("confirm")),1)])),_:1},8,["onClick"])])),_:1})])),_:1})])),_:1},8,["modelValue"]),a.showCustomModal?(g(),d(i,{key:2,class:"custom-modal-mask",onClick:T(l.closeCustomModal,["self"])},{default:u((()=>[p(i,{class:"custom-modal"},{default:u((()=>[p(i,{class:"custom-modal-title"},{default:u((()=>[k("下载提示")])),_:1}),p(i,{class:"custom-modal-content"},{default:u((()=>[k(" 下载地址已复制成功，请前往浏览器粘贴下载 ")])),_:1}),p(i,{class:"custom-modal-footer"},{default:u((()=>[p(I,{class:"custom-modal-btn",onClick:l.closeCustomModal},{default:u((()=>[k("知道了")])),_:1},8,["onClick"])])),_:1})])),_:1})])),_:1},8,["onClick"])):m("",!0),a.showAutoLoginLoading?(g(),d(i,{key:3,class:"auto-login-loading"},{default:u((()=>[p(i,{class:"loading-spinner"}),p(c,{class:"loading-text"},{default:u((()=>[k("自动登录中...")])),_:1})])),_:1})):m("",!0)])),_:1})}],["__scopeId","data-v-a892640b"]]);export{D as default};
