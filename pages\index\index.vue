<template>
	<view class="container">
		<!-- 轮播图部分 -->
		<swiper class="swiper" circular :indicator-dots="true" :autoplay="true" :interval="5000" :duration="2000" :previous-margin="'20rpx'" :next-margin="'20rpx'" :display-multiple-items="1" :easing-function="'easeInOutCubic'">
			<swiper-item v-for="(item, index) in bannerList" :key="index" class="swiper-item">
				<image 
					:src="getImageUrl(item.imageUrl)" 
					mode="aspectFill" 
					class="swiper-image" 
					@click="handleBannerClick(item)"
				/>
			</swiper-item>
		</swiper>

		<!-- 公告栏部分 -->
		<view class="notice-bar" v-if="notice">
			<view class="notice-left">
				<text class="notice-label">{{ t('notice') }}</text>
			</view>
			<view class="notice-right">
				<view class="notice-content">
					<view class="marquee-text">{{ notice.content + '&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' + notice.content }}</view>
				</view>
			</view>
		</view>

		<!-- 其它功能区块迁移自个人中心 -->
		<view class="section-card glass-effect animate-item">
			<!-- <view class="section-title">其它功能</view> -->
			<view class="grid-menu">
				<view 
					class="menu-item" 
					v-for="(item, index) in serviceMenu" 
					:key="index"
					@click="handleMenuClick(item)"
				>
					<image :src="item.icon" class="menu-icon" mode="aspectFit" />
					<text class="menu-text">{{ item.text }}</text>
				</view>
			</view>
		</view>
		<!-- 客服二维码弹窗 -->
		<view class="qrcode-popup" v-if="showQRCode" @click="closeQRCode">
			<view class="popup-content" @click.stop>
				<view class="close-btn" @click="closeQRCode">×</view>
				<image class="qrcode-image" src="/static/qrcode.png" mode="aspectFit"></image>
				<text class="popup-text">扫一扫上面的二维码，加我为好友</text>
			</view>
		</view>

		<!-- 市场趋势板块 -->
		<view class="market-trend-section">
			<view class="market-trend-card">
				<view class="market-trend-header">
					<text class="market-trend-title">热门合约</text>
				</view>
				<view class="market-trend-content">
					<!-- 骨架屏 -->
					<view v-if="isLoading">
						<view v-for="n in skeletonCount" :key="n" class="trend-item">
							<view class="coin-logo skeleton-bg"></view>
							<view class="coin-info">
								<view class="skeleton-text skeleton-title"></view>
								<view class="skeleton-tag"></view>
							</view>
							<view class="coin-price">
								<view class="skeleton-text skeleton-price"></view>
								<view class="skeleton-text skeleton-cny"></view>
							</view>
						</view>
					</view>
					<!-- 实际数据 -->
					<view v-else>
						<view
							v-for="item in trendList"
							:key="item.pairName"
							class="trend-item"
							@click="goToKline(item)"
						>
							<!-- 左侧logo -->
							<image :src="getImageUrl(item.logoUrl)" class="coin-logo" mode="aspectFit" />
							<!-- 中间币种英文名 -->
							<view class="coin-info">
								<text class="coin-en">{{ item.pairName.replace('USDT', '') }}</text>
							</view>
							<!-- 右侧价格和单位 -->
							<view class="coin-price" style="display: flex; align-items: center; justify-content: space-between;">
								<view style="display: flex; flex-direction: column; align-items: flex-end;">
									<text class="price-main">
										{{
											Number(item.price) >= 1
												? Number(item.price).toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })
												: Number(item.price).toFixed(8).replace(/0+$/, '').replace(/\.$/, '')
										}}
									</text>
									<text class="price-cny">¥{{ item.cnyPrice ? item.cnyPrice : '--' }}</text>
								</view>
								<view
									class="change-tag"
									:class="Number(item.change) > 0 ? 'rise' : 'fall'"
								>
									<text class="change-icon">
										<text v-if="Number(item.change) > 0">+</text>
										<text v-else>-</text>
									</text>
									{{ Math.abs(Number(item.change)).toFixed(2) }}%
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>

	 
	 
	</view>
</template>

<script>
	import request from '@/utils/request.js'
	import tokenCheck from '@/mixins/tokenCheck.js'
	import config from '@/config/index.js'
	import { t } from '@/utils/i18n.js'

	export default {
		mixins: [tokenCheck],
		data() {
			return {
				statusBarHeight: 20,
				baseURL: config.apiBaseUrl,
				// 轮播图数据，默认两张本地图片
				bannerList: [
					// { imageUrl: '/static/banner/1.jpg', title: 'Banner 1' },
					// { imageUrl: '/static/banner/2.jpg', title: 'Banner 2' }
				],
				notice: null,    // 公告数据
				trendList: [], // 新增行情数据
				eventSource: null, // 新增SSE对象
				sseCheckTimer: null, // SSE连接检查定时器
				sseReconnectAttempts: 0, // SSE重连尝试次数
				maxReconnectAttempts: 5, // 最大重连次数
				isLoading: true, // 骨架屏加载状态
				skeletonCount: 8, // 骨架条数
				serviceMenu: [
					{ icon: '/static/tools/platform.png', text: '平台介绍' },
					{ icon: '/static/tools/notice.png', text: '官方公告' },
					{ icon: '/static/tools/kefu.png', text: '官方客服' },
					{ icon: '/static/tools/setting.png', text: '设置' }
				],
				showQRCode: false,
			}
		},
		created() {
			// 获取状态栏高度
			const systemInfo = uni.getSystemInfoSync()
			this.statusBarHeight = systemInfo.statusBarHeight
		},
		onShow() {
			this.loadUserInfo()
			this.checkTokenStatus()
			this.loadData()
			// 检查并重新建立SSE连接（应用从后台回到前台时）
			if (typeof window !== 'undefined' && window.EventSource) {
				// 重置重连计数，允许重新尝试
				this.sseReconnectAttempts = 0;

				// 延迟一点时间确保页面完全显示
				setTimeout(() => {
					this.checkSSEConnection();
				}, 500);

				// 启动定时检查SSE连接状态
				this.startSSEHealthCheck();
			}
		},
		onLoad() {
			this.checkTokenStatus()
			this.loadData()
			if (typeof window !== 'undefined' && window.EventSource) {
				this.initSSE();
				// 启动定时检查SSE连接状态
				this.startSSEHealthCheck();
			}
		},
		onHide() {
			// 应用进入后台时关闭SSE连接以节省资源
			if (this.eventSource) {
				this.eventSource.close();
				this.eventSource = null;
			}
			// 停止定时检查
			this.stopSSEHealthCheck();
		},
		onUnload() {
			if (this.eventSource) {
				this.eventSource.close();
				this.eventSource = null;
			}
			// 停止定时检查
			this.stopSSEHealthCheck();
		},
		methods: {
			t,
			// 加载用户信息
			async loadUserInfo() {
				try {
					const res = await request({
						url: '/api/user/info',
						method: 'GET'
					})
					if (res.code === 200 && res.data) {
						uni.setStorageSync('userInfo', res.data)
						if (!res.data.status) {
							uni.redirectTo({ url: '/pages/login/index' })
							return
						}
					}
				} catch (e) {
					console.error('获取用户信息失败:', e)
					uni.showToast({
						title: t('fetchLatestDataFail'),
						icon: 'none'
					})
				}
			},
			handleBannerClick(item) {
				if (item.link) {
					uni.navigateTo({ url: item.link })
				}
			},
			// 加载所有数据
			async loadData() {
				const isTokenValid = await this.checkTokenStatus()
				if (!isTokenValid) return
				try {
					await Promise.all([
						this.loadBannerList(),
						this.loadNotice()
					])
				} catch (e) {
					console.error('加载数据失败:', e)
					if (e.message === '请先登录' || e.statusCode === 401) {
						this.clearLoginInfo()
						this.redirectToLogin()
					} else {
						uni.showToast({
							title: t('loadDataFail'),
							icon: 'none'
						})
					}
				}
			},
			// 加载轮播图数据
			async loadBannerList() {
				try {
					const res = await request({
						url: '/api/banner/list',
						method: 'GET'
					})
					if(res.code === 200 && res.data && res.data.length > 0) {
						this.bannerList = res.data.map(item => ({
							id: item.id,
							imageUrl: item.imageUrl,
							title: item.title
						}))
					}
					// 如果没有数据则保留默认
				} catch(e) {
					console.error('获取轮播图失败:', e)
					// 保持默认bannerList
				}
			},
			// 加载公告数据
			async loadNotice() {
				try {
					const res = await request({
						url: '/api/notice/active',
						method: 'GET'
					})
					if(res.code === 200) {
						this.notice = res.data
					}
				} catch(e) {
					console.error('获取公告失败:', e)
					throw e
				}
			},
			getImageUrl(imageUrl) {
				if (!imageUrl) return '';
				// 如果是/static/开头，直接返回本地静态资源路径
				if (imageUrl.startsWith('/static/')) {
					return imageUrl;
				}
				// 其它情况拼接 baseURL
				if (imageUrl.startsWith('/')) {
					return `${this.baseURL}${imageUrl}`;
				}
				return imageUrl;
			},
			initSSE() {
				// 先关闭现有连接
				if (this.eventSource) {
					this.eventSource.close();
					this.eventSource = null;
				}

				const token = uni.getStorageSync('token') || '';
				if (!token) {
					console.log('SSE初始化失败：缺少token');
					return;
				}

				try {
					console.log('正在初始化SSE连接...');
					this.eventSource = new window.EventSource(
						this.baseURL + '/api/market/trend/stream?token=' + encodeURIComponent(token)
					);

					this.eventSource.onopen = () => {
						console.log('SSE连接已建立');
						// 连接成功，重置重连计数
						this.sseReconnectAttempts = 0;
					};

					this.eventSource.onmessage = (event) => {
						let data = event.data;
						if (data.startsWith('data: ')) {
							data = data.slice(6);
						}
						try {
							const arr = JSON.parse(data);
							arr.forEach(item => {
								item.cnyPrice = (Number(item.price) * 7).toFixed(2);
							});
							this.trendList = arr;
							this.isLoading = false;
							console.log('SSE数据更新成功，数据条数:', arr.length);
						} catch (e) {
							console.error('SSE数据解析失败', e);
							this.isLoading = false;
						}
					};

					this.eventSource.onerror = (e) => {
						console.error('SSE连接异常', e);
						if (this.eventSource) {
							this.eventSource.close();
							this.eventSource = null;
						}
						this.isLoading = false;

						// 限制重连次数，避免无限重连
						if (this.sseReconnectAttempts < this.maxReconnectAttempts) {
							this.sseReconnectAttempts++;
							const delay = Math.min(3000 * this.sseReconnectAttempts, 30000); // 递增延迟，最大30秒
							console.log(`SSE重连第${this.sseReconnectAttempts}次，${delay/1000}秒后重试...`);

							setTimeout(() => {
								if (typeof window !== 'undefined' && window.EventSource && !this.eventSource) {
									this.initSSE();
								}
							}, delay);
						} else {
							console.log('SSE重连次数已达上限，停止重连');
						}
					};
				} catch (error) {
					console.error('SSE初始化失败:', error);
					this.isLoading = false;
				}
			},

			// 检查SSE连接状态
			checkSSEConnection() {
				if (!this.eventSource || this.eventSource.readyState === EventSource.CLOSED) {
					if (this.sseReconnectAttempts < this.maxReconnectAttempts) {
						console.log('SSE连接已断开，尝试重新连接...');
						if (typeof window !== 'undefined' && window.EventSource) {
							this.initSSE();
						}
					} else {
						console.log('SSE重连次数已达上限，跳过重连');
					}
				} else if (this.eventSource.readyState === EventSource.CONNECTING) {
					console.log('SSE正在连接中...');
				} else if (this.eventSource.readyState === EventSource.OPEN) {
					console.log('SSE连接正常');
				}
			},

			// 启动SSE健康检查
			startSSEHealthCheck() {
				// 先清除现有定时器
				this.stopSSEHealthCheck();

				// 每30秒检查一次SSE连接状态
				this.sseCheckTimer = setInterval(() => {
					this.checkSSEConnection();
				}, 30000);
			},

			// 停止SSE健康检查
			stopSSEHealthCheck() {
				if (this.sseCheckTimer) {
					clearInterval(this.sseCheckTimer);
					this.sseCheckTimer = null;
				}
			},

			goToKline(item) {
				uni.navigateTo({
					url: `/pages/kline/index?symbol=${encodeURIComponent(item.pairName)}&from=home`
				});
			},
			handleMenuClick(item) {
				if (item.text === '官方客服') {
					this.showQRCode = true
				} else if (item.text === '平台介绍') {
					uni.navigateTo({
						url: '/pages/legalsystem/index'
					})
				} else if (item.text === '软件设置') {
					uni.navigateTo({
						url: '/pages/accountset/index'
					})
				} else if (item.text === '官方公告') {
					uni.navigateTo({
						url: '/pages/noticelist/index'
					})
				}
			},
			closeQRCode() {
				this.showQRCode = false
			}
		}
	}
</script>

<style lang="scss" scoped>
@import '../../styles/theme.scss';

/* 组件样式 */
.container {
	padding-top: 0;
	min-height: 100vh;
	background: #121212;
	position: relative;
	
	&::before {
		 content: '';
		 position: absolute;
		 top: calc(var(--status-bar-height) + 54px);
		 left: 0;
		 right: 0;
		 height: 300rpx;
		 background: linear-gradient(90deg, #232323, #121212);
		 opacity: 0.08;
		 filter: blur(80px);
	}
}

/* 黑色/深色背景上的所有文字统一为白色 */
.container,
.market-trend-section,
.market-trend-card,
.trend-item,
.coin-info,
.coin-en,
.price-main,

.market-trend-title,
.market-trend-header,
.market-trend-content,
.navbar-content .app-name,
.notice-bar,
.notice-label,
.marquee-text {
  color: #fff !important;
}
.price-cny{
	color: #5E5E5E !important;
	font-size: 24rpx !important;
	font-weight: normal;
	margin-top: 4rpx;
	display: block;
}
/* 金色背景上的文字为黑色（如有） */
button,
.tab-list .tab.active {
  color: #111 !important;
}
/* 涨跌色不变 */
.change-tag[style*='#ff4d4f'], .change-icon[style*='#ff4d4f'], .red { color: #FF4D4F !important; }
.change-tag[style*='#52c41a'], .change-icon[style*='#52c41a'], .green { color: #52c41a !important; }
/* 价格单位也用白色 */
.price-unit { color: #fff !important; }

// 首先定义一个通用边框动画混
@mixin breathing-border {
  position: relative;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: inherit;
    padding: 1px;
    background: linear-gradient(
      135deg,
      rgba(34, 209, 238, 0.3),
      rgba(61, 90, 241, 0.3)
    );
    mask: linear-gradient(#fff 0 0) content-box,
          linear-gradient(#fff 0 0);
    mask-composite: exclude;
    pointer-events: none;
    animation: borderBreath 3s ease-in-out infinite;
  }
}

// ::v-deep .uni-swiper-slides,

 

// 应用公告栏
.notice-bar {
  margin: 20rpx;
  padding: 0 20rpx;
  background: #18191D;
  backdrop-filter: blur(10px);
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  height: 88rpx;
  box-shadow: 0 4rpx 24rpx rgba(255, 215, 0, 0.08);
  
  .notice-left {
    padding: 0 30rpx;
    border-right: 1px solid rgba(255, 215, 0, 0.08);
    height: 100%;
    display: flex;
    align-items: center;
    
    .notice-label {
      color: #ffd700;
      font-size: 32rpx;
      font-weight: 500;
    }
  }
  
  .notice-right {
    flex: 1;
    padding: 0 30rpx;
    overflow: hidden;
    display: flex;
    align-items: center;
    
    .notice-content {
      width: 100%;
      height: 44rpx;
      position: relative;
      overflow: hidden;
      display: flex;
      align-items: center;
      
      .marquee-text {
        position: absolute;
        white-space: nowrap;
        color: #ffd700;
        font-size: 28rpx;
        animation: marquee 40s linear infinite;
      }
    }
  }
}

@keyframes marquee {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-50%);
  }
}

// 确保用吸边框效果
.notice-bar {
  // @include breathing-border; // 已删除
}

// 首页导航栏样式
.home-navbar {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	z-index: 999;
	background: #121212;
	border-bottom: 1px solid rgba(255, 215, 0, 0.08);
	
	.navbar-content {
		height: 44px;
		display: flex;
		align-items: center;
		padding: 0 30rpx;
		margin-bottom: 5px;
		
		.logo-container {
			display: flex;
			align-items: center;
			gap: 12rpx;
			min-width: 0;
			flex-shrink: 1;
			
			.logo-icon {
				width: 40px;
				height: 40px;
				border-radius: 8px;
				margin-right: 10px;
				vertical-align: middle;
				object-fit: cover;
				overflow: hidden;
				box-shadow: 0 0 15rpx rgba(255, 215, 0, 0.15);
				
				// 添加内发光效果
				&::after {
					content: '';
					position: absolute;
					inset: 0;
					border: 1px solid rgba(255, 215, 0, 0.2);
					border-radius: inherit;
					box-shadow: inset 0 0 10rpx rgba(255, 215, 0, 0.12);
					animation: glowPulse 2s ease-in-out infinite;
				}
			}
			
			.app-name {
				color: #ffd700;
				font-size: 34rpx;
				font-weight: bold;
				background: none;
				-webkit-background-clip: unset;
				-webkit-text-fill-color: unset;
				text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
				letter-spacing: 2px;
				white-space: nowrap;
				overflow: hidden;
				text-overflow: ellipsis;
				max-width: 140px;
				display: block;
			}
		}
	}
}

@keyframes rotate {
	from {
		transform: rotate(0deg);
	}
	to {
		transform: rotate(360deg);
	}
}

.distribution-section {
	padding: 10rpx;
	margin: 0;
	background: #181818;
	
	.distribution-card {
		background: #232323;
		border-radius: 16rpx;
		padding: 20rpx;
		box-shadow: 0 0 20rpx rgba(255, 215, 0, 0.10);
		border: 1px solid #ffd70022;
		margin-bottom: 0;
		
		.distribution-header {
			margin-bottom: 20rpx;
			padding: 0 20rpx;
			
			.distribution-title {
				color: #ffd700;
				font-size: 32rpx;
				font-weight: 600;
				text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
				padding: 10rpx 0;
			}
		}
		
		.device-stats {
			display: flex;
			align-items: center;
			padding: 15rpx 20rpx;
			margin-bottom: 30rpx;
			background: #181818;
			border-radius: 8rpx;
			
			.stats-item {
				color: #ffd70099;
				font-size: 26rpx;
				padding: 5rpx 0;
				
				&.online {
					color: #ffd700;
				}
				
				&.offline {
					color: #ffd700;
				}
				
				&.expire {
					color: #daa520;
				}
			}
			
			.divider {
				color: #ffd70044;
				margin: 0 20rpx;
				font-size: 24rpx;
			}
		}
		
		.chart-container {
			width: 100%;
			height: 700rpx;
			margin-top: 10rpx;
			padding-bottom: 20rpx;
			
			.echarts-box {
				width: 100%;
				height: 100%;
			}
		}
	}
}

// 添加旋转动画
@keyframes rotate {
	from {
		 transform: rotate(0deg);
	}
	to {
		 transform: rotate(360deg);
	}
}

// 添加光晕动画
@keyframes shine {
	0%, 100% {
		opacity: 0.3;
	}
	50% {
		opacity: 0.6;
	}
}

// 添加悬浮动画
@keyframes float {
	0%, 100% {
		transform: translateY(0);
	}
	50% {
		transform: translateY(-10rpx);
	}
}

.activate-content {
	padding: 5rpx;
	
	.info-box {
		background: rgba(0, 0, 0, 0.05);
		border-radius: 12rpx;
		padding: 15rpx;
		margin-bottom: 15rpx;
		
		.info-title {
			font-size: 28rpx;
			color: #666;
			margin-bottom: 10rpx;
		}
		
		.info-item {
			display: flex;
			justify-content: space-between;
			font-size: 28rpx;
			color: #333;
			margin: 8rpx 0;
			
			.value {
				color: #22d1ee;
				font-weight: 500;
			}
		}
	}
	
	.activate-input {
		width: 100%;
		height: 80rpx;
		background: #ffffff;
		border: 1px solid #cccccc;
		border-radius: 4rpx;
		padding: 0 15rpx;
		font-size: 28rpx;
		box-sizing: border-box;
		margin: 10rpx 0;
		box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
	}
}

.market-trend-section {
	margin: 30rpx 20rpx 0 20rpx;
	.market-trend-card {
		background: #18191D;
		border-radius: 20rpx;
		box-shadow: 0 0 20rpx rgba(255, 215, 0, 0.10);
		padding: 32rpx 28rpx 36rpx 28rpx;
		.market-trend-header {
			margin-bottom: 18rpx;
			.market-trend-title {
				color: #ffd700;
				font-size: 34rpx;
				font-weight: bold;
				letter-spacing: 2rpx;
				text-shadow: 0 2rpx 8rpx rgba(255,215,0,0.12);
			}
		}
		.market-trend-content {
			min-height: 120rpx;
			// 这里可根据后续内容扩展
		}
	}
}

.market-trend-content {
	.trend-item {
		display: flex;
		align-items: center;
		background: #18191D;
		border-radius: 18rpx;
		margin-bottom: 18rpx;
		padding: 14rpx 20rpx;
		box-shadow: 0 2rpx 12rpx rgba(255,215,0,0.04);
		.coin-logo {
			width: 59rpx;
			height: 59rpx;
			border-radius: 50%;
			margin-right: 24rpx;
			background: #181818;
		}
		.coin-info {
			flex: 1;
			display: flex;
			flex-direction: column;
			justify-content: center;
			.coin-en {
				font-size: 32rpx;
				font-weight: normal;
				color: #fff;
				margin-bottom: 8rpx;
			}
			.change-tag {
				display: inline-flex;
				align-items: center;
				justify-content: center;
				font-size: 20rpx;
				font-weight: 500;
				// border-radius: 1rpx !important;
				padding: 0 70rpx;
				height: 36rpx;
				width: 110rpx;
				min-width: 110rpx;
				max-width: 110rpx;
				box-sizing: border-box;
				margin: 0 4rpx;
				 
				.change-icon {
					font-size: 22rpx;
					margin-right: 6rpx;
				}
			}
		}
		.coin-price {
			text-align: right;
			min-width: 180rpx;
			.price-main {
				font-size: 32rpx;
				font-weight: normal;
				color: #fff;
				margin-right: 8rpx;
			}
			.price-unit {
				font-size: 22rpx;
				color: #ffd700;
			}
			.price-cny {
				font-size: 18rpx;
				font-weight: normal;
				color: #5E5E5E;
				margin-top: 4rpx;
				display: block;
			}
		}
	}
}

.skeleton-bg {
	background: linear-gradient(90deg, #222 25%, #333 50%, #222 75%);
	animation: skeleton-loading 1.2s infinite linear;
}
.skeleton-text {
	height: 28rpx;
	background: linear-gradient(90deg, #222 25%, #333 50%, #222 75%);
	border-radius: 8rpx;
	margin-bottom: 12rpx;
	animation: skeleton-loading 1.2s infinite linear;
}
.skeleton-title { width: 80rpx; height: 32rpx; }
.skeleton-tag { width: 110rpx; height: 36rpx; border-radius: 8rpx; background: #222; margin-bottom: 8rpx; }
.skeleton-price { width: 120rpx; }
.skeleton-cny { width: 80rpx; height: 22rpx; }

@keyframes skeleton-loading {
	0% { background-position: -200rpx 0; }
	100% { background-position: 200rpx 0; }
}

// 美化轮播图样式
.swiper {
  margin: 32rpx 24rpx 0 24rpx;
  border-radius: 20rpx;
  overflow: hidden;
  background: #18191D;
  box-shadow: 0 8rpx 32rpx rgba(255,215,0,0.10), 0 2rpx 8rpx rgba(0,0,0,0.10);
  min-height: 320rpx;
}
.swiper-item {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 320rpx;
  background: #18191D;
}
.swiper-image {
  width: 100%;
  height: 320rpx;
  border-radius: 18rpx;
  box-shadow: 0 4rpx 24rpx rgba(255,215,0,0.10), 0 2rpx 8rpx rgba(0,0,0,0.10);
  object-fit: cover;
  background: #18191D;
  transition: box-shadow 0.3s;
}
.swiper-image:hover {
  box-shadow: 0 8rpx 32rpx rgba(255,215,0,0.18), 0 4rpx 16rpx rgba(0,0,0,0.16);
}

.change-tag {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-size: 22rpx;
  font-weight: 500;
  border-radius: 2px;
  padding: 10rpx 18rpx;
  margin-left: 12rpx;
  color: #fff !important;
  min-width: 120rpx;
  box-sizing: border-box;
  text-align: center;
  
}
.change-tag.rise {
background: #02BF87 !important;
 
  color: #fff !important;
}
.change-tag.fall {
   background: #F34A69 !important;
  color: #fff !important;
}

.section-card {
  background: #18191D;
  border-radius: 18rpx;
  margin: 24rpx 20rpx 0 20rpx;
  padding: 32rpx 24rpx 24rpx 24rpx;
  box-shadow: 0 2rpx 12rpx rgba(0,0,0,0.08);
}

.section-title {
  font-size: 28rpx;
  color: #fff;
  font-weight: 500;
  margin-bottom: 24rpx;
  letter-spacing: 2rpx;
}

.grid-menu {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  gap: 24rpx 0;
}

.menu-item {
  width: 25%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  padding: 18rpx 0 8rpx 0;
  border-radius: 12rpx;
//   background: rgba(255,255,255,0.02);
//   transition: box-shadow 0.2s, background 0.2s;
  cursor: pointer;
//   box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.04);

  &:active {
    background: rgba(255,255,255,0.08);
    box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.10);
  }
}

.menu-icon {
  width: 56rpx;
  height: 56rpx;
  margin-bottom: 10rpx;
  border-radius: 8rpx;
  /* background: #232323; */
  object-fit: contain;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.08);
}

.menu-text {
  font-size: 22rpx;
  color: #fff;
  margin-top: 2rpx;
  text-align: center;
  letter-spacing: 1rpx;
}

/* 客服二维码弹窗样式 */
.qrcode-popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  z-index: 999;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: fadeIn 0.3s ease;
}
.popup-content {
  background: #fff;
  border-radius: 20rpx;
  width: 500rpx;
  padding: 25rpx 0rpx;
  position: relative;
  animation: scaleIn 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.close-btn {
  position: absolute;
  right: 10rpx;
  top: 10rpx;
  width: 60rpx;
  height: 60rpx;
  line-height: 60rpx;
  text-align: center;
  font-size: 40rpx;
  color: #999;
  font-weight: bold;
  z-index: 10;
  cursor: pointer;
}
.qrcode-image {
  width: 400rpx;
  height: 400rpx;
  margin: 0 auto;
  display: block;
}
.popup-text {
  font-size: 28rpx;
  color: #333;
  text-align: center;
  display: block;
  margin-top: 10rpx;
  padding: 0 15rpx;
}
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}
@keyframes scaleIn {
  from { transform: scale(0.8); opacity: 0; }
  to { transform: scale(1); opacity: 1; }
}

</style>
