<template>
	<view class="binance-container">
		<!-- 顶部导航+时间 -->
		<view class="custom-navbar">
			<view class="navbar-content">
				<view class="left-area" @click="handleBack">
					<uni-icons type="left" size="22" color="#fff"></uni-icons>
			</view>
				<view class="pair-area">
					<text class="page-title">{{ currentSymbol.label }}</text>
			</view>
				<view class="right-area">
					
				</view>
			</view>
		</view>

		<view class="main-scroll-area">
			<!-- 价格区块 -->
		<view class="binance-header-pixel">
			<view v-if="headerLoading" class="skeleton-header-pixel">
				<view class="header-loading-content">
					<view class="skeleton-price-line"></view>
					<view class="skeleton-info-grid">
						<view class="skeleton-info-item"></view>
						<view class="skeleton-info-item"></view>
						<view class="skeleton-info-item"></view>
						<view class="skeleton-info-item"></view>
					</view>
				</view>
			</view>
			<template v-else>
				<view class="binance-header-pixel-row">
					<view class="binance-header-pixel-left">
						<text class="binance-header-pixel-price" :style="{color: mainPriceColor}">{{ formatPrice(ticker.price) }}</text>
						<view class="binance-header-pixel-cny-row">
							<text class="binance-header-pixel-cny" :style="{color: mainPriceColor}">${{ formatPrice(ticker.price) }}</text>
							<text class="binance-header-pixel-rate" :style="{color: mainPriceColor}">{{ ticker.changeRate ? (ticker.changeRate>0?'+':'')+ticker.changeRate+'%' : '' }}</text>
						</view>
					</view>
					<view class="binance-header-pixel-right">
						<view class="binance-header-pixel-right-row">
							<view class="binance-header-pixel-right-col">
								<text class="binance-header-pixel-label">最高</text>
								<text class="binance-header-pixel-value">{{ formatPrice(ticker.high) }}</text>
							</view>
							<view class="binance-header-pixel-right-col">
								<text class="binance-header-pixel-label">最低</text>
								<text class="binance-header-pixel-value">{{ formatPrice(ticker.low) }}</text>
							</view>
						</view>
						<view class="binance-header-pixel-right-row">
							<view class="binance-header-pixel-right-col">
								<text class="binance-header-pixel-label">成交量</text>
								<text class="binance-header-pixel-value">{{ formatPrice(ticker.vol) }}</text>
							</view>
							<view class="binance-header-pixel-right-col">
								<text class="binance-header-pixel-label">成交额</text>
								<text class="binance-header-pixel-value">{{ formattedAmount }}</text>
							</view>
						</view>
					</view>
				</view>
			</template>
		</view>

			<!-- 周期切换栏 -->
		<view class="binance-period-row">
			<view class="period-left-items">
				<text v-for="item in availableMainPeriods" :key="item.value" :class="['period-text', { 'active': period === item.value, 'disabled': isChangingPeriod }]" @click="changePeriod(item.value)">{{item.label}}</text>
				<text v-if="isPeriodsAvailable(['1d'])" :class="['period-text', { 'active': period === '1d', 'disabled': isChangingPeriod }]" @click="changePeriod('1d')">日线</text>
				<text v-if="availableMorePeriods.length > 0" :class="['period-text', { 'disabled': isChangingPeriod }]" @click="!isChangingPeriod && (showMorePopup = true)">更多<uni-icons type="down" size="12" color="#fff"></uni-icons> </text>

			</view>
			<!-- 移除指标按钮 -->
		</view>

			<!-- 更多周期弹窗 -->
		<view v-if="showMorePopup" class="period-popup-mask" @click.self="closePeriodPopup" @touchmove.prevent>
			<view class="period-popup" @click.stop>
				<view class="period-popup-header">
					<view class="period-popup-title">时间周期</view>
					<view class="period-popup-close" @click="closePeriodPopup">
						<uni-icons type="close" size="20" color="#999"></uni-icons>
					</view>
				</view>
				<view class="period-popup-grid">
					<button v-for="item in availableMorePeriods" :key="item.value"
							:class="['period-popup-btn', { 'active': period === item.value }]"
							@click="selectMorePeriod(item.value)">{{item.label}}</button>
				</view>
			</view>
		</view>

			<!-- K线+副图区域（不动） -->
		<view class="binance-chart-area">
  <view v-if="chartLoading" class="skeleton-chart">
    <view class="chart-loading-text">
      <text class="loading-title">正在加载K线数据...</text>
      <text class="loading-subtitle">{{ currentSymbol.label }}</text>
    </view>
  </view>
  <template v-else>
    <template v-if="option">
      <buouyuEcharts :option="option" canvasId="kline" height="300px" width="97%" />
    </template>
    <template v-else>
      <view v-if="_noKlineData" style="color:#fff;text-align:center;padding:40px 0;">暂无数据</view>
      <view v-else style="color:#fff;text-align:center;padding:40px 0;">加载中...</view>
    </template>
  </template>

  <!-- 回到最新数据按钮 -->
  <view v-if="isUserBrowsingHistory" class="back-to-latest-btn" @click="backToLatest">
    <text class="back-to-latest-text">回到最新</text>
  </view>
</view>

			<!-- 盈利/持仓/成交tab和表格区域，完全复用futures页面结构 -->
<view class="futures-tabs">
  <view class="tab-list">
    <view :class="['tab', activeTab==='hold'?'active':'']" @click="onTabChange('hold')">持仓</view>
    <view :class="['tab', activeTab==='deal'?'active':'']" @click="onTabChange('deal')">成交</view>
    <view :class="['tab', activeTab==='profit'?'active':'']" @click="onTabChange('profit')">盈利</view>
  </view>
  <view class="tab-table" v-if="activeTab==='profit'" style="min-height: 600rpx;">
    <view class="table-header">
      <text>时间</text>
      <text>方向</text>
      <text>获利</text>
      <text>平仓价</text>
    </view>
    <view v-if="profitList.length === 0 && !loading" class="empty-state">
      <text class="empty-text">暂无盈利数据</text>
    </view>
    <view class="table-row" v-for="(item, idx) in profitList" :key="item.id">
      <text>{{ formatTime(item.settleTime) }}</text>
      <text :style="item.direction==='up' ? 'color: #02BF87' : 'color: #F34A69'">{{ item.direction==='up'?'买涨':'买跌' }}</text>
      <text :class="item.profit > 0 ? 'red' : 'green'">{{ item.profit }}</text>
      <text>{{ item.closePrice }}</text>
    </view>
    <view v-if="profitHasMore" class="table-more">
      <button v-if="profitHasMore" @click="onProfitLoadMore" :disabled="loading">加载更多</button>
      <text v-else style="color:#fff;font-size:22rpx;">已经没有了</text>
    </view>
  </view>
  <view v-if="activeTab==='hold'" class="hold-card-list" style="min-height: 600rpx;">
    <view v-if="holdList.length === 0 && !loading" class="empty-state">
      <text class="empty-text">暂无持仓数据</text>
    </view>
    <view v-for="(item, idx) in holdList" :key="item.id" class="hold-card">
      <view class="hold-row-2col">
        <view class="hold-col">
          <text class="hold-label">时间</text>
          <text class="hold-value">{{ formatTime(item.orderTime) }}</text>
        </view>
        <view class="hold-col">
          <text class="hold-label">方向</text>
          <text class="hold-value" :style="item.direction==='up' ? 'color: #02BF87' : 'color: #F34A69'">
            {{ item.direction==='up'?'买涨':'买跌' }}
          </text>
        </view>
      </view>
      <view class="hold-row-2col">
        <view class="hold-col">
          <text class="hold-label">金额</text>
          <text class="hold-value">{{ item.amount }}</text>
        </view>
        <view class="hold-col">
          <text class="hold-label">开仓价</text>
          <text class="hold-value">{{ item.openPrice }}</text>
        </view>
      </view>
      <view class="hold-row-2col">
        <view class="hold-col">
          <text class="hold-label">盈利</text>
          <text class="hold-value" :class="item.profit > 0 ? 'red' : 'green'">{{ item.profit !== undefined ? item.profit : '--' }}</text>
        </view>
        <view class="hold-col">
          <text class="hold-label">收益率</text>
          <text class="hold-value" :class="item.percent > 0 ? 'red' : 'green'">{{ item.percent !== undefined ? (item.percent * 100).toFixed(2) + '%' : '--' }}</text>
        </view>
      </view>
      <view class="hold-row-2col">
        <view class="hold-col">
          <text class="hold-label">状态</text>
          <text class="hold-value">
            {{ item.status === 0 ? '待成交' : item.status === 1 ? '已成交' : '--' }}
          </text>
        </view>
      </view>
    </view>
  </view>
  <view class="tab-table" v-if="activeTab==='deal'" style="min-height: 600rpx;">
    <view class="table-header">
      <text>时间</text>
      <text>方向</text>
      <text>盈亏</text>
      <text>平仓价</text>
    </view>
    <view v-if="dealList.length === 0 && !loading" class="empty-state">
      <text class="empty-text">暂无成交数据</text>
    </view>
    <view class="table-row" v-for="(item, idx) in dealList" :key="item.id">
      <text>{{ formatTime(item.settleTime) }}</text>
      <text :style="item.direction==='up' ? 'color: #02BF87' : 'color: #F34A69'">{{ item.direction==='up'?'买涨':'买跌' }}</text>
      <text :class="item.profit > 0 ? 'red' : 'green'">{{ item.profit }}</text>
      <text :class="item.profit > 0 ? 'red' : 'green'">{{ item.closePrice }}</text>
    </view>
    <view v-if="dealHasMore" class="table-more">
      <button v-if="dealHasMore" @click="onDealLoadMore" :disabled="loading">加载更多</button>
      <text v-else style="color:#fff;font-size:22rpx;">已经没有了</text>
    </view>
  </view>
</view>

		</view>

		<!-- 底部tab和按钮 -->
		<view class="binance-bottom-bar new-bar">
			<button class="buy-up-btn" @click="openOrderPopup('up')">
				<text class="btn-icon">📈</text> 买涨
			</button>
			<button class="buy-down-btn" @click="openOrderPopup('down')">
				<text class="btn-icon">📉</text> 买跌
			</button>
		</view>

		<!-- 下单弹窗 -->
		<view v-if="showOrderPopup" class="order-popup-mask" @click.self="closeOrderPopup">
			<view class="order-popup">
				<!-- 杠杆选择 -->
				<view class="order-period-row">
					<view :class="['order-period-btn', {active: orderLeverage===5}]" @click="orderLeverage=5">X5</view>
					<view :class="['order-period-btn', {active: orderLeverage===10}]" @click="orderLeverage=10">X10</view>
				</view>
				<view class="order-popup-info">
					<view class="order-popup-row">
						<text class="order-popup-label">可用</text>
						<text class="order-popup-value">{{ availableBalance }}USDT</text>
					</view>
					<view class="order-popup-row">
						<text class="order-popup-label">买入数量</text>
						<input class="order-popup-input" v-model="orderAmount" placeholder="请输入买入数量" type="number" />
					</view>
					<view class="order-popup-row">
						<text class="order-popup-label">止盈</text>
						<input class="order-popup-input" v-model="takeProfit" placeholder="请输入止盈价" type="number" />
					</view>
					<view class="order-popup-row">
						<text class="order-popup-label">止损</text>
						<input class="order-popup-input" v-model="stopLoss" placeholder="请输入止损价" type="number" />
					</view>
					<view class="order-popup-row">
						<text class="order-popup-label">保证金</text>
						<text class="order-popup-value">{{ orderAmount && orderLeverage ? (Number(orderAmount) / Number(orderLeverage)).toFixed(4) : '--' }}</text>
					</view>
				</view>
				<button class="order-confirm-btn" :class="orderDirection==='down'?'down':''" @click="onOrderConfirm">
					{{ orderDirection==='up' ? '买涨' : '买跌' }}
				</button>
			</view>
		</view>
	</view>
</template>

<script>
import buouyuEcharts from 'buouyu-echarts'
import request from '@/utils/request.js'
import config from '@/config/index.js'
// 币安K线周期映射
const periodMap = {
	'time': {interval: '1m', label: '分时'},
	'15m': {interval: '15m', label: '15分钟'},
	'1h': {interval: '1h', label: '1小时'},
	'4h': {interval: '4h', label: '4小时'},
	'1d': {interval: '1d', label: '日线'},
	'1s': {interval: '1s', label: '1秒'},
	'1m': {interval: '1m', label: '1分钟'},
	'3m': {interval: '3m', label: '3分钟'},
	'5m': {interval: '5m', label: '5分钟'},
	'30m': {interval: '30m', label: '30分钟'},
	'2h': {interval: '2h', label: '2小时'},
	'6h': {interval: '6h', label: '6小时'},
	'8h': {interval: '8h', label: '8小时'},
	'12h': {interval: '12h', label: '12小时'},
	'3d': {interval: '3d', label: '3日'},
	'1w': {interval: '1w', label: '周线'},
	'1M': {interval: '1M', label: '月线'}
}

// 主流币对列表
const symbolList = [
	{ value: 'BTCUSDT', label: 'BTC/USDT' },
	{ value: 'ETHUSDT', label: 'ETH/USDT' },
	{ value: 'BNBUSDT', label: 'BNB/USDT' },
	{ value: 'SOLUSDT', label: 'SOL/USDT' },
	{ value: 'DOGEUSDT', label: 'DOGE/USDT' },
	{ value: 'XRPUSDT', label: 'XRP/USDT' },
	{ value: 'ADAUSDT', label: 'ADA/USDT' },
	{ value: 'TRXUSDT', label: 'TRX/USDT' }
]

// 计算均线
function calcMA(dayCount, data) {
	const result = []
	for (let i = 0; i < data.length; i++) {
		if (i < dayCount - 1) {
			result.push('-')
			continue
		}
		let sum = 0
		for (let j = 0; j < dayCount; j++) {
			sum += data[i - j][1] // 收盘价
		}
		result.push((sum / dayCount).toFixed(2))
	}
	return result
}

function calcMA_single(dayCount, data) {
	const result = [];
	for (let i = 0; i < data.length; i++) {
		if (i < dayCount - 1) {
			result.push('-');
			continue;
		}
		let sum = 0;
		for (let j = 0; j < dayCount; j++) {
			sum += data[i - j];
		}
		result.push((sum / dayCount).toFixed(2));
	}
	return result;
}

// 计算MACD
function calcMACD(data, short=12, long=26, signal=9) {
	let emaShort = []
	let emaLong = []
	let dif = []
	let dea = []
	let macd = []
	for (let i = 0; i < data.length; i++) {
		const close = data[i][1]
		if (i === 0) {
			emaShort.push(close)
			emaLong.push(close)
		} else {
			emaShort.push(emaShort[i-1] * (short-1)/(short+1) + close * 2/(short+1))
			emaLong.push(emaLong[i-1] * (long-1)/(long+1) + close * 2/(long+1))
		}
		dif.push(emaShort[i] - emaLong[i])
		if (i === 0) {
			dea.push(dif[i])
		} else {
			dea.push(dea[i-1] * (signal-1)/(signal+1) + dif[i] * 2/(signal+1))
		}
		macd.push((dif[i] - dea[i]) * 2)
	}
	return { dif, dea, macd }
}

export default {
	components: {
		buouyuEcharts
	},
	data() {
		return {
			title: 'Hello',
			option: null,
			period: 'time',
			showMorePopup: false,
			isChangingPeriod: false, // 添加切换状态标识
			// 所有可用的周期选项
			allMainPeriods: [
				{ value: 'time', label: '分时' },
				{ value: '15m', label: '15分钟' },
				{ value: '1h', label: '1小时' },
				{ value: '4h', label: '4小时' }
			],
			allMorePeriods: [
				{ value: '1s', label: '1秒' },
				{ value: '1m', label: '1分钟' },
				{ value: '3m', label: '3分钟' },
				{ value: '5m', label: '5分钟' },
				{ value: '30m', label: '30分钟' },
				{ value: '2h', label: '2小时' },
				{ value: '6h', label: '6小时' },
				{ value: '8h', label: '8小时' },
				{ value: '12h', label: '12小时' },
				{ value: '1d', label: '日线' },
				{ value: '3d', label: '3日' },
				{ value: '1w', label: '周线' },
				{ value: '1M', label: '月线' }
			],
			// 实际显示的周期选项（根据缓存过滤）
			mainPeriods: [],
			morePeriods: [],
			symbolList,
			currentSymbol: symbolList[0],
			ticker: {
				price: '--',
				cnyPrice: '',
				changeRate: '',
				high: '',
				low: '',
				vol: '',
				amount: ''
			},
			websocket: null,
			categoryData: [],
			values: [],
			volumes: [],
			closes: [],
			rawTimestamps: [],
			bids: [],
			asks: [],
			profitTab: 'profit',
			symbol: '',
			fromPage: '',
			_noKlineData: false,
			activeTab: 'hold', // 当前tab，默认显示持仓
			profitList: [], // 盈利列表
			profitPage: 1,
			pageSize: 10,
			profitTotal: 0,
			profitHasMore: true,
			holdList: [], // 持仓列表
			holdPage: 1,
			holdTotal: 0,
			holdHasMore: true,
			dealList: [], // 成交列表
			dealPage: 1,
			dealTotal: 0,
			dealHasMore: true,
			loading: false, // 加载状态
			holdProfitSSE: null, // 新增SSE对象
			showOrderPopup: false,
			orderDirection: 'up',
			orderLeverage: 5,
			orderAmount: '',
			takeProfit: '',
			stopLoss: '',
			availableBalance: 0,
			chartLoading: true,
			headerLoading: true,
			baseURL: config.apiBaseUrl,
			debounceTimer: null, // 防抖定时器
			isInitializing: false, // 防止重复初始化
			reconnectTimer: null, // SSE重连定时器
			isDestroyed: false, // 页面是否已销毁
			tickerAbortController: null, // ticker请求控制器
			klineAbortController: null, // K线请求控制器
			depthAbortController: null, // 深度请求控制器
			// 新增：跟踪用户是否正在手动浏览历史数据
			isUserBrowsingHistory: false,
			// 保存当前的dataZoom范围
			currentDataZoomStart: null,
			currentDataZoomEnd: null,
			// 浏览历史数据的定时器
			browsingTimer: null
		}
	},
	onLoad(options) {
		this.symbol = options.symbol || '';
		this.fromPage = options.from || '';
		// 初始化可用周期和恢复选择
		this.initializePeriods();
		if (this.symbol) {
			this.loadKlineData(this.symbol);
		}
	},
	computed: {
		// 根据缓存过滤可用的周期选项
		availableMainPeriods() {
			const availablePeriods = this.getAvailablePeriodsFromCache();
			return this.allMainPeriods.filter(period =>
				availablePeriods.includes(period.value)
			);
		},

		availableMorePeriods() {
			const availablePeriods = this.getAvailablePeriodsFromCache();
			return this.allMorePeriods.filter(period =>
				availablePeriods.includes(period.value)
			);
		},

		priceColor() {
			if (this.ticker.changeRate > 0) return '#02BF87'; // 上涨 - 绿色
			if (this.ticker.changeRate < 0) return '#F34A69'; // 下跌 - 红色
			return '#333';
		},
		mainPriceColor() {
			if (this.ticker.price === '--') return '#fff'; // 加载/暂无数据
			if (this.ticker.changeRate > 0) return '#02BF87'; // 上涨 - 绿色
			if (this.ticker.changeRate < 0) return '#F34A69'; // 下跌 - 红色
			return '#fff';
		},
		baseAsset() {
			return this.currentSymbol.value.replace('USDT', '');
		},
		formattedAmount() {
			const amount = this.ticker.amount;
			if (amount === '' || amount === '--' || amount === null || isNaN(amount)) {
				return '--';
			}
			if (amount >= 100000000) {
				return (amount / 100000000).toFixed(2) + '亿';
			}
			if (amount >= 10000) {
				return (amount / 10000).toFixed(2) + '万';
			}
			return parseFloat(amount).toFixed(2);
		},
		depthList() {
			const list = [];
			const depth = 15; 
			const asks = [...this.asks].reverse();

			for (let i = 0; i < depth; i++) {
				const bid = this.bids[i] || [null, null];
				const ask = asks[i] || [null, null];
				list.push({
					bidPrice: bid[0] ? parseFloat(bid[0]).toFixed(2) : '--',
					bidQty: bid[1] ? parseFloat(bid[1]).toFixed(4) : '--',
					askPrice: ask[0] ? parseFloat(ask[0]).toFixed(2) : '--',
					askQty: ask[1] ? parseFloat(ask[1]).toFixed(4) : '--',
				});
			}
			return list;
		},
		expectedProfit() {
			// 假设40%收益率
			const amt = Number(this.orderAmount);
			if (!amt || isNaN(amt)) return 0;
			return (amt * 0.4).toFixed(2);
		}
	},
	mounted() {
		// mounted中不需要重复初始化，onLoad已经处理了
		// 只需要初始化一些不依赖于参数的功能
	},
	onUnload() {
		// 标记页面已销毁
		this.isDestroyed = true;

		// 清理SSE连接
		if (this.websocket) {
			this.websocket.close();
			this.websocket = null;
		}

		// 清理定时器
		if (this.debounceTimer) {
			clearTimeout(this.debounceTimer);
			this.debounceTimer = null;
		}

		if (this.reconnectTimer) {
			clearTimeout(this.reconnectTimer);
			this.reconnectTimer = null;
		}

		// 取消所有进行中的请求
		if (this.tickerAbortController) {
			this.tickerAbortController.abort();
			this.tickerAbortController = null;
		}

		if (this.klineAbortController) {
			this.klineAbortController.abort();
			this.klineAbortController = null;
		}

		if (this.depthAbortController) {
			this.depthAbortController.abort();
			this.depthAbortController = null;
		}

		// 清理持仓盈利SSE
		if (this.holdProfitSSE) {
			this.holdProfitSSE.close();
			this.holdProfitSSE = null;
		}
	},
	watch: {
		period() {
			// 添加防抖，避免快速切换时重复请求
			this.debounceSetupWebSocket();
		},
		'currentSymbol.value'() {
			// 添加防抖，避免快速切换时重复请求
			this.debounceSetupWebSocket();
		},
		showOrderPopup(val) {
			if (val) {
				document.body.addEventListener('click', this.handleGlobalClick, true);
			} else {
				document.body.removeEventListener('click', this.handleGlobalClick, true);
			}
		}
	},
	methods: {
		// 防抖方法，避免快速切换时重复请求
		debounceSetupWebSocket() {
			if (this.debounceTimer) {
				clearTimeout(this.debounceTimer);
			}
			this.debounceTimer = setTimeout(() => {
				this.setupWebSocket();
			}, 300); // 300ms防抖延迟
		},

		formatPrice(val) {
			if (val === undefined || val === null || val === '' || isNaN(val)) return '--';
			let num = Number(val);
			if (num === 0) return '0';
			// 转字符串，去掉小数点后多余的0
			let str = num.toString();
			if (str.indexOf('.') > -1) {
				str = str.replace(/(\.\d*?[1-9])0+$/, '$1').replace(/\.0+$/, '');
			}
			return str;
		},
		async fetchDepth() {
			// 取消之前的请求
			if (this.depthAbortController) {
				this.depthAbortController.abort();
			}

			this.depthAbortController = new AbortController();

			try {
				const symbol = this.currentSymbol.value;
				const url = this.baseURL + `/api/market/kline/depth?symbol=${symbol}&limit=20`
				const res = await fetch(url, {
					signal: this.depthAbortController.signal
				});
				const data = await res.json();
				this.bids = data.bids || [];
				this.asks = data.asks || [];
			} catch(e) {
				if (e.name !== 'AbortError') {
					console.error("Failed to fetch depth", e);
				}
			}
		},
		handleDepthMessage(data) {
			this.bids = data.bids || [];
			this.asks = data.asks || [];
		},
		onSymbolChange(e) {
			this.currentSymbol = this.symbolList[e.detail.value];
			// 使用统一的初始化方法
			this.initializeKlineData();
		},
		async changePeriod(val) {
			if (this.period === val || this.isChangingPeriod) return; // 避免重复切换

			// 检查周期是否可用
			const availablePeriods = this.getAvailablePeriodsFromCache();
			if (!availablePeriods.includes(val)) {
				uni.showToast({
					title: '该周期暂不可用',
					icon: 'none'
				});
				return;
			}

			this.isChangingPeriod = true;
			this.period = val;
			this.closePeriodPopup();

			// 缓存用户选择的周期
			this.savePeriodToCache(val);

			// 平滑切换，避免页面高度突然变化
			await this.smoothPeriodChange();

			this.isChangingPeriod = false;
		},
		selectMorePeriod(val) {
			if (this.period === val || this.isChangingPeriod) return; // 避免重复切换

			// 检查周期是否可用
			const availablePeriods = this.getAvailablePeriodsFromCache();
			if (!availablePeriods.includes(val)) {
				uni.showToast({
					title: '该周期暂不可用',
					icon: 'none'
				});
				return;
			}

			this.isChangingPeriod = true;
			this.period = val;
			this.closePeriodPopup();

			// 缓存用户选择的周期
			this.savePeriodToCache(val);

			// 平滑切换，避免页面高度突然变化
			this.smoothPeriodChange().then(() => {
				this.isChangingPeriod = false;
			});
		},

		// 缓存周期选择
		savePeriodToCache(period) {
			try {
				uni.setStorageSync('kline_selected_period', period);
			} catch (e) {
				console.error('保存周期缓存失败:', e);
			}
		},

		// 恢复周期选择
		restorePeriodFromCache() {
			try {
				const cachedPeriod = uni.getStorageSync('kline_selected_period');
				const availablePeriods = this.getAvailablePeriodsFromCache();

				// 如果缓存的周期在可用列表中，则使用缓存的周期
				if (cachedPeriod && availablePeriods.includes(cachedPeriod)) {
					this.period = cachedPeriod;
				} else {
					// 否则使用第一个可用的周期
					this.period = availablePeriods.length > 0 ? availablePeriods[0] : 'time';
				}
			} catch (e) {
				console.error('恢复周期缓存失败:', e);
				this.period = 'time'; // 默认值
			}
		},

		// 获取可用的周期列表（从缓存或用户权限）
		getAvailablePeriodsFromCache() {
			try {
				// 尝试从缓存获取可用周期配置
				const availablePeriods = uni.getStorageSync('available_periods');
				if (availablePeriods && Array.isArray(availablePeriods)) {
					return availablePeriods;
				}

				// 如果没有缓存，检查用户信息来确定可用周期
				const userInfo = uni.getStorageSync('userInfo');
				if (userInfo) {
					return this.getPeriodsBasedOnUserLevel(userInfo);
				}

				// 默认返回所有周期
				return this.getDefaultAvailablePeriods();
			} catch (e) {
				console.error('获取可用周期失败:', e);
				return this.getDefaultAvailablePeriods();
			}
		},

		// 根据用户等级获取可用周期
		getPeriodsBasedOnUserLevel(userInfo) {
			// 这里可以根据用户的VIP等级、权限等来返回不同的周期
			// 示例逻辑：
			const isVip = userInfo.isVip || false;
			const isLeader = userInfo.isLeader || false;
			const userLevel = userInfo.level || 0;
			const availableBalance = parseFloat(userInfo.availableBalance || '0');

			// VIP用户或团队长或高余额用户可以使用所有周期
			if (isVip || isLeader || userLevel >= 3 || availableBalance >= 1000) {
				return this.getDefaultAvailablePeriods();
			}
			// 中级用户可以使用大部分周期
			else if (userLevel >= 2 || availableBalance >= 100) {
				return ['time', '15m', '1h', '4h', '1d', '1m', '3m', '5m', '30m', '2h', '6h', '1w'];
			}
			// 普通用户可以使用基础周期
			else if (userLevel >= 1 || availableBalance >= 10) {
				return ['time', '15m', '1h', '4h', '1d', '1m', '5m', '30m'];
			}
			// 新用户只能使用最基础的周期
			else {
				return ['time', '15m', '1h', '1d'];
			}
		},

		// 获取默认可用周期
		getDefaultAvailablePeriods() {
			return [
				'time', '15m', '1h', '4h', '1d',
				'1s', '1m', '3m', '5m', '30m',
				'2h', '6h', '8h', '12h', '3d', '1w', '1M'
			];
		},

		// 检查指定周期是否可用
		isPeriodsAvailable(periods) {
			const availablePeriods = this.getAvailablePeriodsFromCache();
			return periods.some(period => availablePeriods.includes(period));
		},

		// 设置可用周期（可以从服务器获取后调用）
		setAvailablePeriods(periods) {
			try {
				uni.setStorageSync('available_periods', periods);
				// 重新检查当前选中的周期是否还可用
				if (!periods.includes(this.period)) {
					this.period = periods.length > 0 ? periods[0] : 'time';
					this.savePeriodToCache(this.period);
				}
			} catch (e) {
				console.error('设置可用周期失败:', e);
			}
		},

		// 初始化周期设置
		async initializePeriods() {
			try {
				// 可以从服务器获取用户的可用周期配置
				await this.fetchAvailablePeriodsFromServer();
				// 恢复用户上次选择的周期
				this.restorePeriodFromCache();
			} catch (e) {
				console.error('初始化周期失败:', e);
				// 如果获取失败，使用本地逻辑
				this.restorePeriodFromCache();
			}
		},

		// 从服务器获取可用周期（可选功能）
		async fetchAvailablePeriodsFromServer() {
			try {
				// 这里可以调用API获取用户的可用周期配置
				// const res = await request({
				//   url: '/api/user/available-periods',
				//   method: 'GET'
				// });
				// if (res.code === 200 && res.data) {
				//   this.setAvailablePeriods(res.data.periods);
				// }

				// 暂时使用本地逻辑
				const userInfo = uni.getStorageSync('userInfo');
				if (userInfo) {
					const periods = this.getPeriodsBasedOnUserLevel(userInfo);
					this.setAvailablePeriods(periods);
				}
			} catch (e) {
				console.error('从服务器获取可用周期失败:', e);
			}
		},

		// 关闭周期弹窗
		closePeriodPopup() {
			this.showMorePopup = false;
		},

		// 平滑切换周期，避免页面高度突然变化
		async smoothPeriodChange() {
			// 先显示加载状态，保持当前图表高度
			this.chartLoading = true;

			// 延迟一小段时间，让用户看到切换反馈
			await new Promise(resolve => setTimeout(resolve, 100));

			// 重新设置WebSocket连接
			this.setupSSE();
		},

		async fetchTicker() {
			this.headerLoading = true;

			// 取消之前的请求
			if (this.tickerAbortController) {
				this.tickerAbortController.abort();
			}

			this.tickerAbortController = new AbortController();

			try {
				const symbol = this.currentSymbol.value
				const url = this.baseURL + `/api/market/kline/ticker?symbol=${symbol}`
				const res = await fetch(url, {
					signal: this.tickerAbortController.signal
				})
				const data = await res.json()
				const price = data.price;
				const changeRate = data.changeRate;
				const high = data.high;
				const low = data.low;
				const vol = data.vol;
				const amount = data.amount;
				const usdt2cny = 7.2
				const cnyPrice = Number(price) * usdt2cny;
				this.ticker = { price: this.formatPrice(price), cnyPrice: this.formatPrice(cnyPrice), changeRate: this.formatPrice(changeRate), high: this.formatPrice(high), low: this.formatPrice(low), vol: this.formatPrice(vol), amount: this.formatPrice(amount) }
			} catch (e) {
				if (e.name !== 'AbortError') {
					console.error('获取ticker失败:', e);
					this.ticker = { price: '--', cnyPrice: '', changeRate: '', high: '', low: '', vol: '', amount: '' }
				}
			} finally {
				this.headerLoading = false;
			}
		},
		async fetchBinanceKline() {
			this.chartLoading = true;

			// 取消之前的请求
			if (this.klineAbortController) {
				this.klineAbortController.abort();
			}

			this.klineAbortController = new AbortController();

			try {
				let interval = periodMap[this.period]?.interval || '1d'
				if(interval === '1s') interval = '1m'
				const symbol = this.currentSymbol.value
				const limit = 100
				const url = this.baseURL + `/api/market/kline/kline?symbol=${symbol}&interval=${interval}&limit=${limit}`
				const res = await fetch(url, {
					signal: this.klineAbortController.signal
				})
				const data = await res.json()
				if (data.kline) {
					this.rawTimestamps = data.kline.rawTimestamps || []
					this.categoryData = data.kline.categoryData || []
					this.values = data.kline.values || []
					this.volumes = data.kline.volumes || []
					this.closes = data.kline.closes || []
					this.updateChartOption();
				}
			} catch (e) {
				if (e.name !== 'AbortError') {
					console.error('获取K线数据失败', e);
					this.option = null
				}
			} finally {
				this.chartLoading = false;
			}
		},
		updateChartOption() {
			if (this.categoryData.length === 0) {
				return
			}
			if(this.period === 'time') {
				const latestClose = this.closes.length > 0 ? this.closes[this.closes.length - 1] : 0;
				const volumeFormatter = (value) => {
					if (!value) return 0
					if (value > 1000000) return (value / 1000000).toFixed(2) + 'M';
					if (value > 1000) return (value / 1000).toFixed(2) + 'K';
					return value.toFixed(2);
				};
				
				const ma60 = calcMA(60, this.values);
				const rawVolumes = this.volumes.map(v => v.value);
				const volMA5 = calcMA_single(5, rawVolumes);
				const volMA10 = calcMA_single(10, rawVolumes);
				const macdData = calcMACD(this.values);
				const macdStyledData = macdData.macd.map((value, i) => {
					const prevValue = i > 0 ? macdData.macd[i - 1] : 0;
					const style = {};
					if (value >= 0) { if (value >= prevValue) { style.color = '#02BF87'; } else { style.color = '#fff'; style.borderColor = '#02BF87'; style.borderWidth = 1; } }
					else { if (value < prevValue) { style.color = '#F34A69'; } else { style.color = '#fff'; style.borderColor = '#F34A69'; style.borderWidth = 1; } }
					return { value: value, itemStyle: style };
				});

				// 如果用户正在浏览历史数据，保持当前范围；否则显示最新数据
				let end, start;
				if (this.isUserBrowsingHistory && this.currentDataZoomStart !== null && this.currentDataZoomEnd !== null) {
					// 保持用户当前浏览的范围
					start = this.currentDataZoomStart;
					end = this.currentDataZoomEnd;
				} else {
					// 默认显示最新的32个数据点
					end = this.categoryData.length - 1;
					start = Math.max(0, end - 32);
					// 更新当前范围
					this.currentDataZoomStart = start;
					this.currentDataZoomEnd = end;
				}

				this.option = {
					backgroundColor: '#181A20',
					tooltip: {
						trigger: 'axis',
						axisPointer: {
							type: 'cross',
							label: {
								backgroundColor: '#fff',
								color: '#000',
								padding: [0, 0, 0, 12]
							}
						}
					},
					title: [
						{ text: `MA(60): ${ma60[ma60.length - 1]}`, left: '2%', top: '2%', textStyle: { fontSize: 10, color: '#fff' } },
						{ text: `Vol: ${volumeFormatter(rawVolumes[rawVolumes.length-1])} MA(5):${volMA5[volMA5.length-1]} MA(10):${volMA10[volMA10.length-1]}`, left: '2%', top: '65%', textStyle: { fontSize: 10, color: '#FFD700' } },
						{ text: `DIF:${macdData.dif[macdData.dif.length-1].toFixed(2)} DEA:${macdData.dea[macdData.dea.length-1].toFixed(2)} MACD:${macdData.macd[macdData.macd.length-1].toFixed(2)}`, left: '2%', top: '94%', textStyle: { fontSize: 10, color: '#FFD700' } }
					],
					grid: [
							{ left: '2%', right: 50, top: '10%', height: '30%', bottom: 0 },   // 主图
							{ left: '2%', right: 50, top: '45%', height: '18%', bottom: 0 },   // 成交量
							{ left: '2%', right: 50, top: '68%', height: '20%', bottom: '6%' }   // MACD，底部留空间
							],
					xAxis: [
						{ type: 'category', data: this.categoryData, scale: true, boundaryGap: false, axisLine: { show: false }, axisTick: { show: false }, splitLine: { show: false }, axisLabel: { show: false }, gridIndex: 0 },
						{ type: 'category', data: this.categoryData, gridIndex: 1, axisLabel: { show: false }, axisTick: { show: false }, axisLine: { show: false }, splitLine: { show: false } },
						{ type: 'category', data: this.categoryData, gridIndex: 2, axisLabel: { show: true, color: '#fff', fontSize: 10 }, axisTick: { show: false }, axisLine: { show: false }, splitLine: { show: false } }
					],
					yAxis: [
						{
							scale: true,
							gridIndex: 0,
							splitLine: { show: true, lineStyle: { color: '#333' } },
							axisLine: { show: false },
							axisTick: { show: false },
							position: 'right', // 价格刻度在最右侧
							axisLabel: {
								inside: false, // 外侧
								align: 'right',
								margin: 50,
								color: '#fff',
								fontSize: 10,
								fontWeight: 'bold',
								formatter: val => parseFloat(val).toFixed(2)
							}
						},
						{ gridIndex: 1, splitNumber: 2, splitLine: { show: true, lineStyle: { color: '#333' } }, axisLabel: { show: true, align: 'right', margin: 32, color: '#fff', fontSize: 10, formatter: volumeFormatter }, axisLine: { show: false }, axisTick: { show: false }, position: 'right' },
						{ gridIndex: 2, splitNumber: 2, splitLine: { show: true, lineStyle: { color: '#333' } }, axisLabel: { show: true, align: 'right', margin: 32, color: '#fff', fontSize: 10 }, axisLine: { show: false }, axisTick: { show: false }, position: 'right' }
					],
					series: [
						{ type: 'line', data: this.closes, smooth: true, showSymbol: false, areaStyle: { color: 'rgba(255, 255, 255, 0.10)' }, lineStyle: { color: '#fff', width: 1.5 }, xAxisIndex: 0, yAxisIndex: 0, markLine: { symbol: 'none', silent: true, data: [{ yAxis: latestClose, lineStyle: { color: '#fff', type: 'dashed' }, label: { show: true, position: 'end', formatter: '{c}', backgroundColor: '#fff', color: '#000', padding: [2, 4], borderRadius: 4, fontSize: 10 } }] } },
						{ name: 'MA60', type: 'line', data: ma60, smooth: true, showSymbol: false, lineStyle: { color: '#fff', width: 1 }, xAxisIndex: 0, yAxisIndex: 0 },
						{ name: 'Volume', type: 'bar', data: this.volumes, xAxisIndex: 1, yAxisIndex: 1, barWidth: '80%' },
						{ name: 'Vol MA5', type: 'line', data: volMA5, smooth: true, showSymbol: false, lineStyle: { color: '#fff', width: 1 }, xAxisIndex: 1, yAxisIndex: 1 },
						{ name: 'Vol MA10', type: 'line', data: volMA10, smooth: true, showSymbol: false, lineStyle: { color: '#E5C07B', width: 1 }, xAxisIndex: 1, yAxisIndex: 1 },
						{ name: 'MACD', type: 'bar', data: macdStyledData, xAxisIndex: 2, yAxisIndex: 2, barWidth: '80%' },
						{ name: 'DIF', type: 'line', data: macdData.dif, xAxisIndex: 2, yAxisIndex: 2, smooth: true, showSymbol: false, lineStyle: { color: '#fff' } },
						{ name: 'DEA', type: 'line', data: macdData.dea, xAxisIndex: 2, yAxisIndex: 2, smooth: true, showSymbol: false, lineStyle: { color: '#fff' } }
					],
					graphic: [{ type: 'text', left: 'center', top: '35%', style: { text: 'CATCOIN', fill: 'rgba(255, 215, 0, 0.05)', font: 'bold 50px sans-serif' } }],
					dataZoom: [
						{
							type: 'inside',
							xAxisIndex: [0, 1, 2],
							startValue: start,
							endValue: end,
							zoomOnMouseWheel: true,
							moveOnMouseMove: true,
							moveOnMouseWheel: true,
							preventDefaultMouseMove: false,
							zoomLock: false,
							throttle: 100
						}
					]
				}
			} else {
				const ma5 = calcMA(5, this.values)
				const macdData = calcMACD(this.values)
				const rawVolumes = this.volumes.map(v => v.value);
				const volMA5 = calcMA_single(5, rawVolumes);
				const volMA10 = calcMA_single(10, rawVolumes);

				// Create styled data for MACD histogram
				const macdStyledData = macdData.macd.map((value, i) => {
					const prevValue = i > 0 ? macdData.macd[i - 1] : 0;
					const style = {};
					if (value >= 0) { // Green
						if (value >= prevValue) { // Solid
							style.color = '#02BF87';
						} else { // Hollow
							style.color = '#fff';
							style.borderColor = '#02BF87';
							style.borderWidth = 1;
						}
					} else { // Red
						if (value < prevValue) { // Solid
							style.color = '#F34A69';
						} else { // Hollow
							style.color = '#fff';
							style.borderColor = '#F34A69';
							style.borderWidth = 1;
						}
					}
					return { value: value, itemStyle: style };
				});

				const latestMA5 = parseFloat(ma5[ma5.length - 1]).toFixed(2);
				const latestClose = this.closes[this.closes.length - 1];
				const latestVolume = this.volumes.length > 0 ? this.volumes[this.volumes.length-1].value : 0;
				const latestVolMA5 = volMA5.length > 0 && volMA5[volMA5.length - 1] !== '-' ? parseFloat(volMA5[volMA5.length - 1]).toFixed(2) : '--';
				const latestVolMA10 = volMA10.length > 0 && volMA10[volMA10.length - 1] !== '-' ? parseFloat(volMA10[volMA10.length - 1]).toFixed(2) : '--';
				const latestDIF = macdData.dif.length > 0 ? parseFloat(macdData.dif[macdData.dif.length-1]).toFixed(2) : '0.00';
				const latestDEA = macdData.dea.length > 0 ? parseFloat(macdData.dea[macdData.dea.length-1]).toFixed(2) : '0.00';
				const latestMACD = macdData.macd.length > 0 ? parseFloat(macdData.macd[macdData.macd.length-1]).toFixed(2) : '0.00';
				
				const volumeFormatter = (value) => {
					if (!value) return 0
					if (value > 1000000) return (value / 1000000).toFixed(2) + 'M';
					if (value > 1000) return (value / 1000).toFixed(2) + 'K';
					return value.toFixed(2);
				};
				
				// 如果用户正在浏览历史数据，保持当前范围；否则显示最新数据
				let end, start;
				if (this.isUserBrowsingHistory && this.currentDataZoomStart !== null && this.currentDataZoomEnd !== null) {
					// 保持用户当前浏览的范围
					start = this.currentDataZoomStart;
					end = this.currentDataZoomEnd;
				} else {
					// 默认显示最新的32个数据点
					end = this.categoryData.length - 1;
					start = Math.max(0, end - 32);
					// 更新当前范围
					this.currentDataZoomStart = start;
					this.currentDataZoomEnd = end;
				}
				this.option = {
					backgroundColor: '#181A20',
					tooltip: {
						trigger: 'axis',
						axisPointer: {
							type: 'cross',
							label: {
								backgroundColor: '#fff',
								color: '#000',
							}
						}
					},
					title: [
						{
							text: `MA(5): ${latestMA5}`,
							left: '2%', top: '2%',
							textStyle: { fontSize: 10, color: '#fff' }
						},
						{
							text: `Vol: ${volumeFormatter(latestVolume)} MA(5):${latestVolMA5} MA(10):${latestVolMA10}`,
							left: '2%', top: '65%',
							textStyle: { fontSize: 10, color: '#fff' }
						},
						{
							text: `DIF:${latestDIF} DEA:${latestDEA} MACD:${latestMACD}`,
							left: '2%', top: '94%',
							textStyle: { fontSize: 10, color: '#fff' }
						}
					],
					grid: [

							{ left: '2%', right: 50, top: '10%', height: '30%', bottom: 0 },   // 主图
							{ left: '2%', right: 50, top: '45%', height: '18%', bottom: 0 },   // 成交量
							{ left: '2%', right: 50, top: '68%', height: '20%', bottom: '6%' }   // MACD，底部留空间
					],
					xAxis: [
						{ type: 'category', data: this.categoryData, scale: true, boundaryGap: ['0%', '1%'], axisLine: { show: false }, axisTick: { show: false }, splitLine: { show: false }, axisLabel: { show: false }, min: 'dataMin', max: 'dataMax', gridIndex: 0 },
						{ type: 'category', data: this.categoryData, gridIndex: 1, axisLabel: { show: false }, axisTick: { show: false }, axisLine: { show: false }, splitLine: { show: false } },
						{ type: 'category', data: this.categoryData, gridIndex: 2, axisLabel: { show: true, color: '#fff', fontSize: 10 }, axisTick: { show: false }, axisLine: { show: false }, splitLine: { show: false } }
					],
					yAxis: [
						{ scale: true, gridIndex: 0, splitLine: { show: true, lineStyle: { color: '#333' } }, axisLine: { show: false }, axisTick: { show: false }, position: 'right', axisLabel: { align: 'right', margin: 50, color: '#fff', fontSize: 10, fontWeight: 'bold', formatter: val => parseFloat(val).toFixed(2) } },
						{ gridIndex: 1, splitNumber: 2, splitLine: { show: true, lineStyle: { color: '#333' } }, axisLabel: { show: true, align: 'right', margin: 32, color: '#fff', fontSize: 10, formatter: volumeFormatter }, axisLine: { show: false }, axisTick: { show: false }, position: 'right' },
						{ gridIndex: 2, splitNumber: 2, splitLine: { show: true, lineStyle: { color: '#333' } }, axisLabel: { show: true, align: 'right', margin: 32, color: '#fff', fontSize: 10 }, axisLine: { show: false }, axisTick: { show: false }, position: 'right' }
					],
					series: [
						{
							name: 'K线',
							type: 'candlestick',
							data: this.values,
							xAxisIndex: 0,
							yAxisIndex: 0,
							itemStyle: {
								color: '#02BF87',
								color0: '#F34A69',
								borderColor: '#02BF87',
								borderColor0: '#F34A69'
							},
							barWidth: '80%',
							markLine: {
								symbol: 'none',
								silent: true,
								data: [{
									yAxis: latestClose,
									lineStyle: { color: '#fff', type: 'dashed' },
									label: {
										show: true,
										position: 'end',
										formatter: '{c}',
										backgroundColor: '#fff',
										color: '#000',
										padding: [2, 4],
										borderRadius: 4,
										fontSize: 10
									}
								}]
							}
						},
						{ name: 'MA5', type: 'line', data: ma5, smooth: true, showSymbol: false, lineStyle: { color: '#fff', width: 1.5 }, xAxisIndex: 0, yAxisIndex: 0, symbol: 'none' },
						{ name: 'Volume', type: 'bar', data: this.volumes, xAxisIndex: 1, yAxisIndex: 1, barWidth: '80%' },
						{ name: 'Vol MA5', type: 'line', data: volMA5, smooth: true, showSymbol: false, lineStyle: { color: '#fff', width: 1 }, xAxisIndex: 1, yAxisIndex: 1, symbol: 'none' },
						{ name: 'Vol MA10', type: 'line', data: volMA10, smooth: true, showSymbol: false, lineStyle: { color: '#E5C07B', width: 1 }, xAxisIndex: 1, yAxisIndex: 1, symbol: 'none' },
						{
							name: 'MACD',
							type: 'bar',
							data: macdStyledData,
							xAxisIndex: 2,
							yAxisIndex: 2,
							barWidth: '80%'
						},
						{ name: 'DIF', type: 'line', data: macdData.dif, xAxisIndex: 2, yAxisIndex: 2, smooth: true, showSymbol: false, lineStyle: { color: '#fff' } },
						{ name: 'DEA', type: 'line', data: macdData.dea, xAxisIndex: 2, yAxisIndex: 2, smooth: true, showSymbol: false, lineStyle: { color: '#fff' } }
					],
					graphic: [
						{
							type: 'text',
							left: 'center', top: '35%',
							style: {
								text: 'Kline',
								fill: 'rgba(0, 0, 0, 0.05)',
								font: 'bold 50px sans-serif'
							}
						}
					],
					dataZoom: [
						{
							type: 'inside',
							xAxisIndex: [0, 1, 2],
							startValue: start,
							endValue: end,
							zoomOnMouseWheel: true,
							moveOnMouseMove: true,
							moveOnMouseWheel: true,
							preventDefaultMouseMove: false,
							zoomLock: false,
							throttle: 100
						}
					]
				}
			}
		},
		setupWebSocket() {
			if (this.websocket) {
				this.websocket.close();
			}

			// 重新获取数据并建立连接
			this.initializeKlineData();
		},

		setupSSE() {
			const symbol = this.currentSymbol.value;
			const interval = periodMap[this.period]?.interval || '1m';

			// 关闭现有连接
			if (this.websocket) {
				this.websocket.close();
				this.websocket = null;
			}

			// 清除重连定时器
			if (this.reconnectTimer) {
				clearTimeout(this.reconnectTimer);
				this.reconnectTimer = null;
			}

			// 检查页面是否还存在
			if (this.isDestroyed) {
				return;
			}

			const url = this.baseURL + `/api/market/kline/stream/symbol?symbol=${symbol}&interval=${interval}`;

			try {
				const eventSource = new EventSource(url);

				eventSource.onmessage = (event) => {
					try {
						const data = JSON.parse(event.data);
						this.handleSSEMessage(data);
					} catch (e) {
						console.error('SSE数据解析失败', e);
					}
				};

				eventSource.onerror = (error) => {
					console.error('SSE连接错误:', error);
					eventSource.close();

					// 只有在页面未销毁时才重连
					if (!this.isDestroyed) {
						this.reconnectTimer = setTimeout(() => {
							if (!this.isDestroyed) {
								this.setupSSE();
							}
						}, 5000);
					}
				};

				this.websocket = eventSource;
			} catch (error) {
				console.error('创建SSE连接失败:', error);
			}
		},

		handleSSEMessage(data) {
			// 处理SSE推送的数据
			if (data.type === 'ticker') {
				this.handleTickerMessage(data);
			} else if (data.type === 'kline') {
				this.handleKlineMessage(data);
			} else if (data.type === 'depth') {
				this.handleDepthMessage(data);
			}
		},

		handleTickerMessage(data) {
			const price = data.price;
			const changeRate = data.changeRate;
			const high = data.high;
			const low = data.low;
			const vol = data.vol;
			const amount = data.amount;
			const usdt2cny = 7.2;
			const cnyPrice = Number(price) * usdt2cny;
			this.ticker = { price: this.formatPrice(price), cnyPrice: this.formatPrice(cnyPrice), changeRate: this.formatPrice(changeRate), high: this.formatPrice(high), low: this.formatPrice(low), vol: this.formatPrice(vol), amount: this.formatPrice(amount) };
		},

		handleKlineMessage(data) {
			// data.kline 是后端推送的全量K线对象
			if (!data.kline) return;
			const values = data.kline.values || [];
			// 判断是否为空或全为0
			const isAllZero = values.length > 0 && values.every(arr => Array.isArray(arr) ? arr.every(v => v === 0) : values.every(v => v === 0));
			if (values.length === 0 || isAllZero) {
				this.option = null;
				this._noKlineData = true;
				return;
			}
			this._noKlineData = false;
			// 新增：如kline推送中带ticker，则同步更新顶部数据
			if (data.ticker) {
				this.ticker = {
					price: this.formatPrice(data.ticker.price),
					cnyPrice: this.formatPrice((Number(data.ticker.price) * 7.2)),
					changeRate: this.formatPrice(data.ticker.changeRate),
					high: this.formatPrice(data.ticker.high),
					low: this.formatPrice(data.ticker.low),
					vol: this.formatPrice(data.ticker.vol),
					amount: this.formatPrice(data.ticker.amount)
				};
			}
			this.rawTimestamps = data.kline.rawTimestamps || [];
			this.categoryData = data.kline.categoryData || [];
			this.values = values;
			this.volumes = data.kline.volumes || [];
			this.closes = data.kline.closes || [];
			this.updateChartOption();
		},
			loadKlineData(symbol) {
			const found = this.symbolList.find(item => item.value === symbol);
			if (found) {
				this.currentSymbol = found;
			} else {
				this.currentSymbol = this.symbolList[0];
			}
			// 只调用一次初始化，避免重复请求
			this.initializeKlineData();
		},

		async initializeKlineData() {
			// 防止重复初始化
			if (this.isInitializing) {
				return;
			}

			this.isInitializing = true;
			try {
				// 并行获取初始数据
				await Promise.all([
					this.fetchTicker(),
					this.fetchBinanceKline(),
					this.fetchDepth()
				]);
				// 数据加载完成后再建立SSE连接
				this.setupSSE();
			} catch (error) {
				console.error('初始化K线数据失败:', error);
			} finally {
				this.isInitializing = false;
			}
		},
		handleBack() {
			if (this.fromPage === 'home') {
				uni.switchTab({ url: '/pages/index/index' });
			} else {
				uni.navigateBack();
			}
		},
		// 替换明细区域数据请求和处理逻辑为与futures页面一致
		async loadProfitList(isLoadMore = false) {
			this.loading = true;
			try {
				const res = await request({
					url: '/api/futures/option/profit',
					method: 'GET',
					params: { page: this.profitPage, size: this.pageSize }
				});
				if (res.code === 200) {
					const records = res.data.records || [];
					if (isLoadMore) {
						this.profitList = this.profitList.concat(records);
					} else {
						this.profitList = records;
					}
					this.profitTotal = res.data.total;
					this.profitHasMore = this.profitList.length < this.profitTotal;
				}
			} finally {
				this.loading = false;
			}
		},
		async loadHoldList(isLoadMore = false) {
			this.loading = true;
			try {
				const res = await request({
					url: '/api/futures/option/hold',
					method: 'GET',
					params: { page: this.holdPage, size: this.pageSize }
				});
				if (res.code === 200) {
					const records = res.data.records || [];
					if (isLoadMore) {
						this.holdList = this.holdList.concat(records);
					} else {
						this.holdList = records;
					}
					this.holdTotal = res.data.total;
					this.holdHasMore = this.holdList.length < this.holdTotal;
				}
			} finally {
				this.loading = false;
			}
		},
		async loadDealList(isLoadMore = false) {
			this.loading = true;
			try {
				const res = await request({
					url: '/api/futures/option/deal',
					method: 'GET',
					params: { page: this.dealPage, size: this.pageSize }
				});
				if (res.code === 200) {
					const records = res.data.records || [];
					if (isLoadMore) {
						this.dealList = this.dealList.concat(records);
					} else {
						this.dealList = records;
					}
					this.dealTotal = res.data.total;
					this.dealHasMore = this.dealList.length < this.dealTotal;
				}
			} finally {
				this.loading = false;
			}
		},
		onTabChange(tab) {
			this.activeTab = tab;
			if (tab === 'profit') {
				this.profitPage = 1;
				this.loadProfitList(false);
			} else if (tab === 'hold') {
				this.holdPage = 1;
				this.loadHoldList(false);
			} else if (tab === 'deal') {
				this.dealPage = 1;
				this.loadDealList(false);
			}
		},
		onProfitLoadMore() {
			if (this.loading || !this.profitHasMore) return;
			this.profitPage++;
			this.loadProfitList(true);
		},
		onHoldLoadMore() {
			if (this.loading || !this.holdHasMore) return;
			this.holdPage++;
			this.loadHoldList(true);
		},
		onDealLoadMore() {
			if (this.loading || !this.dealHasMore) return;
			this.dealPage++;
			this.loadDealList(true);
		},
		formatTime(val) {
			if (!val) return '';
			let date;
			if (typeof val === 'string' && val.length >= 19) {
				date = new Date(val);
			} else if (typeof val === 'number' || (typeof val === 'string' && /^\d+$/.test(val))) {
				let num = Number(val);
				if (val.length === 10) {
					date = new Date(num * 1000);
				} else if (val.length === 13) {
					date = new Date(num);
				} else {
					return '--';
				}
			} else {
				return '--';
			}
			const pad = n => n < 10 ? '0' + n : n;
			const h = pad(date.getHours());
			const m = pad(date.getMinutes());
			const s = pad(date.getSeconds());
			return `${h}:${m}:${s}`;
		},
		initHoldProfitSSE() {
			if (this.holdProfitSSE) this.holdProfitSSE.close();
			const token = uni.getStorageSync('token') || '';
			if (!token) return;
			// 'https://webapi.catcoinvip.com/api/futures/option/hold/profit/stream?token=' + encodeURIComponent(token)
			const url = this.baseURL + '/api/futures/option/hold/profit/stream?token=' + encodeURIComponent(token);
			this.holdProfitSSE = new window.EventSource(
				url
			);
			this.holdProfitSSE.onmessage = async (event) => {
				let data = event.data;
				if (data.startsWith('data: ')) data = data.slice(6);
				try {
					const arr = JSON.parse(data);
					let removed = false;
					arr.forEach(item => {
						const idx = this.holdList.findIndex(o => o.id === item.orderId);
						if (idx !== -1) {
							if (item.status === 1) {
								// 已成交，移除
								this.holdList.splice(idx, 1);
								removed = true;
							} else {
								// 更新盈利和收益率
								this.holdList[idx].profit = item.profit;
								this.holdList[idx].percent = item.percent;
							}
						}
					});
					// 如有移除，建议刷新余额（如有余额字段可调用刷新）
				} catch (e) {}
			};
			this.holdProfitSSE.onerror = () => {
				this.holdProfitSSE.close();
				setTimeout(() => this.initHoldProfitSSE(), 5000);
			};
		},
		async loadAvailableBalance() {
			try {
				const res = await request({ url: '/api/user/info', method: 'GET' });
				if (res.code === 200 && res.data) {
					this.availableBalance = res.data.availableBalance;
				}
			} catch (e) {
				this.availableBalance = 0;
			}
		},
		openOrderPopup(direction) {
			this.orderDirection = direction;
			this.showOrderPopup = true;
			this.loadAvailableBalance();
		},
		closeOrderPopup() {
			this.showOrderPopup = false;
		},
		onOrderConfirm: async function() {
			// 显示暂未开放提示
			uni.showToast({
				title: '暂未开放',
				icon: 'none',
				duration: 2000
			});
			// 关闭弹窗
			this.closeOrderPopup();
			return;

			// 以下是原有的下单逻辑，暂时注释
			/*
			if (this.orderLoading) return;
			if (!this.orderAmount || isNaN(this.orderAmount)) {
				uni.showToast({ title: '请输入正确的金额', icon: 'none' });
				return;
			}
			const amt = Number(this.orderAmount);
			if (amt < 1 || amt > 6000) {
				uni.showToast({ title: '金额需在1~6000之间', icon: 'none' });
				return;
			}
			if (amt > Number(this.availableBalance)) {
				uni.showToast({ title: '余额不足', icon: 'none' });
				return;
			}
			if (!this.currentSymbol || !this.currentSymbol.value) {
				uni.showToast({ title: '请选择币对', icon: 'none' });
				return;
			}
			this.orderLoading = true;
			try {
				const res = await request({
					url: '/api/futures/option/order',
					method: 'POST',
					data: {
						symbol: this.currentSymbol.value,
						direction: this.orderDirection,
						amount: amt,
						period: this.orderLeverage
					}
				});
				if (res.code === 200) {
					uni.showToast({ title: '下单成功', icon: 'success' });
					this.orderAmount = '';
					await this.loadAvailableBalance(); // 刷新余额
					this.holdPage = 1;
					this.loadHoldList(false);
					this.closeOrderPopup(); // 关闭弹窗
				} else {
					uni.showToast({ title: res.message || res.msg || '下单失败', icon: 'none' });
				}
			} catch (e) {
				console.error('下单异常:', e);
				uni.showToast({ title: e.message || '下单异常', icon: 'none' });
			} finally {
				this.orderLoading = false;
			}
			*/
		},
		handleGlobalClick(e) {
			// 如果点击在弹窗内容内，不关闭
			const popup = document.querySelector('.order-popup');
			if (popup && popup.contains(e.target)) return;
			this.closeOrderPopup();
		},

		// 用户开始浏览历史数据
		onUserStartBrowsing() {
			console.log('User started browsing history');
			this.isUserBrowsingHistory = true;

			// 设置一个定时器，如果用户停止操作一段时间后，允许自动跳回最新数据
			if (this.browsingTimer) {
				clearTimeout(this.browsingTimer);
			}
			this.browsingTimer = setTimeout(() => {
				console.log('Auto reset browsing state');
				this.isUserBrowsingHistory = false;
				this.currentDataZoomStart = null;
				this.currentDataZoomEnd = null;
			}, 30000); // 30秒后自动重置
		},

		// 回到最新数据
		backToLatest() {
			console.log('Back to latest data');
			this.isUserBrowsingHistory = false;
			this.currentDataZoomStart = null;
			this.currentDataZoomEnd = null;

			// 清除定时器
			if (this.browsingTimer) {
				clearTimeout(this.browsingTimer);
				this.browsingTimer = null;
			}

			// 重新更新图表，显示最新数据
			this.updateChartOption();
		}
	}
		
	}
</script>

<style>
.binance-container {
	min-height: 100vh;
	background: #121212 !important;
}
.binance-navbar {
	display: flex;
	align-items: center;
	justify-content: space-between;
	background: #121212 !important;
	padding: 0 32rpx 0 32rpx;
	height: 64rpx;
	border-bottom: 1rpx solid #333;
}
.binance-navbar-back {
	font-size: 32rpx;
	color: #fff;
	margin-right: 12rpx;
}
.binance-navbar-symbol-picker {
	display: flex;
	align-items: center;
}
.binance-navbar-symbol {
	font-size: 32rpx;
	font-weight: bold;
	margin-right: 4rpx;
	color: #fff;
}
.binance-navbar-arrow {
	font-size: 20rpx;
	color: #fff;
	margin-right: 8rpx;
}
.binance-navbar-icons text {
	font-size: 24rpx;
	margin-left: 16rpx;
	color: #fff;
}
.binance-tabbar-row {
	display: flex;
	align-items: center;
	margin-top: 0;
	border-bottom: 2rpx solid #333;
	background: #181A20;
	padding-left: 32rpx;
}
.binance-tabbar-tab {
	font-size: 22rpx;
	color: #C9B37E;
	padding: 0 24rpx 18rpx 24rpx;
	position: relative;
}
.binance-tabbar-tab-active {
	color: #fff;
	font-weight: bold;
	border-bottom: 4rpx solid #fff;
	background: #181A20;
}
.binance-header {
	padding: 8rpx 8rpx 0 8rpx;
	background: #fff;
}
.binance-price-row {
	display: flex;
	align-items: baseline;
	margin-bottom: 2rpx;
}
.binance-main-price {
	font-size: 32rpx;
	font-weight: bold;
}
.binance-cny-price {
	font-size: 16rpx;
	margin-left: 8rpx;
}
.binance-change-rate {
	font-size: 16rpx;
	margin-left: 8rpx;
}
.binance-info-row {
	display: flex;
	flex-wrap: wrap;
	font-size: 14rpx;
	color: #999;
	gap: 6rpx;
	margin-bottom: 2rpx;
}
.binance-period-row {
	display: flex;
	justify-content: space-between;
	gap: 12rpx;
	margin: 10rpx 0 10rpx 0;
	padding: 0 32rpx;
	align-items: center;
	/* background: #181A20; */
}
.period-left-items, .period-right-items {
	display: flex;
	align-items: center;
	gap: 24rpx;
}
.binance-icon {
	font-size: 32rpx;
	color: #FFD700;
}
.period-text {
	padding: 0 16rpx;
	height: 40rpx;
	line-height: 40rpx;
	display: inline-block;
	font-size: 20rpx;
	color: #fff;
	cursor: pointer;
	background: transparent;
	transition: background 0.2s, color 0.2s;
}
.period-text.active {
	background: #fff !important;
	color: #111 !important;
	border-radius: 8rpx;
	padding: 0 16rpx;
	font-size: 20rpx;
}
.period-text.disabled {
	opacity: 0.5;
	pointer-events: none;
	cursor: not-allowed;
}
.binance-chart-area {
	flex: 1; /* Let chart take up available space */
	min-height: 300px; /* 固定最小高度，与图表高度一致 */
	height: 300px; /* 固定高度，避免切换时跳动 */
	/* background: #181A20; */
	padding: 0;
	margin: 0;
	position: relative; /* 为加载状态定位 */
	display: flex;
	align-items: stretch;
	justify-content: stretch;
	touch-action: pan-x; /* 只允许水平滑动，防止与页面滚动冲突 */
	overflow: hidden; /* 防止内容溢出 */
}
.order-book-container {
	height: 140px; /* Final height adjustment */
	display: flex;
	flex-direction: column;
	background: #fff;
	padding: 0 32rpx;
	font-size: 22rpx;
	color: #333;
}
.order-book-tabs {
	display: flex;
	gap: 24rpx;
	border-bottom: 1rpx solid #f5f5f5;
	margin-bottom: 8rpx;
}
.order-book-tab {
	padding: 12rpx 0;
	color: #888;
}
.order-book-tab.active {
	color: #333;
	font-weight: bold;
	border-bottom: 4rpx solid #f4b400;
}
.order-book-table {
	flex: 1;
	display: flex;
	flex-direction: column;
}
.order-book-header {
	display: flex;
	justify-content: space-between;
	color: #888;
	padding: 4rpx 0;
}
.order-book-header text {
	flex: 1;
	text-align: center;
}
.order-book-rows {
	flex: 1;
	overflow-y: auto; /* Make rows scrollable if they overflow */
}
.order-book-row {
	display: flex;
	justify-content: space-between;
	padding: 2rpx 0;
}
.order-book-row text {
	flex: 1;
	text-align: center;
}
.buy-text {
	color: #02BF87;
}
.sell-text {
	color: #F34A69;
}
.binance-bottom-bar.new-bar {
	display: flex;
	align-items: center;
	justify-content: space-between;
	background: #18191D;
	padding: 10rpx 16rpx;
	gap: 12px;
	min-height: 60px;
}
.bottom-icons {
	display: flex;
	gap: 32rpx;
}
.icon-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 4rpx;
}
.icon {
	font-size: 32rpx;
	font-weight: bold;
}
.icon-text {
	font-size: 20rpx;
	color: #666;
}
.bottom-buttons {
	display: flex;
	gap: 16rpx;
}
.binance-bottom-bar {
	position: fixed;
	left: 0;
	right: 0;
	bottom: 0;
	width: 100vw;
	z-index: 999;
	box-sizing: border-box;
	padding: 0 16px;
	background: #18191D;
}
.binance-bottom-bar button {
	margin: 0 8px;
}
.binance-buy-btn {
	background: #02BF87;
	color: #fff;
	font-size: 20rpx;
	border-radius: 6rpx;
	padding: 8rpx 28rpx;
	border: none;
}
.binance-sell-btn {
	background: #F34A69;
	color: #fff;
	font-size: 20rpx;
	border-radius: 6rpx;
	padding: 8rpx 28rpx;
	border: none;
}
.binance-header-pixel {
	/* background: #181A20; */
	padding: 32rpx 15rpx 10rpx 15rpx;
	border-radius: 24rpx 24rpx 0 0 !important;
}
.binance-header-pixel-row {
	display: flex;
	flex-direction: row;
	justify-content: space-between;
	align-items: flex-start;
}
.binance-header-pixel-left {
	display: flex;
	flex-direction: column;
	align-items: flex-start;
	flex: 1.2;
}
.binance-header-pixel-price {
	font-size: 42rpx;
	font-weight: bold;
	line-height: 1.1;
	margin-bottom: 6rpx;
	color: #fff;
}
.binance-header-pixel-cny-row {
	display: flex;
	align-items: baseline;
	margin-bottom: 6rpx;
}
.binance-header-pixel-cny {
	font-size: 28rpx;
	margin-right: 12rpx;
	color: #fff;
}
.binance-header-pixel-rate {
	font-size: 24rpx;
	font-weight: bold;
	color: #02BF87;
}
.binance-header-pixel-tags {
	display: flex;
	gap: 12rpx;
	margin-bottom: 0;
}
.binance-header-pixel-tag {
	font-size: 20rpx;
	padding: 2rpx 14rpx;
	border-radius: 10rpx;
	background: #222;
	color: #fff;
	font-weight: 500;
	border: 1rpx solid #fff;
}
.binance-header-pixel-tag-yellow {
	background: #333;
	color: #f5f5f5;
}
.binance-header-pixel-tag-orange {
	color: #fff;
	background: #222;
	border: 1rpx solid #FFD700;	
}
.binance-header-pixel-arrow {
	font-size: 20rpx;
	color: #fff;
	font-weight: bold;
}
.binance-header-pixel-right {
	display: flex;
	flex-direction: column;
	align-items: flex-end;
	gap: 8rpx;
	flex: 1;
}
.binance-header-pixel-right-row {
	display: flex;
	flex-direction: row;
	align-items: center;
	gap: 32rpx;
}
.binance-header-pixel-right-col {
	display: flex;
	flex-direction: column;
	align-items: flex-end;
	gap: 2rpx;
}
.binance-header-pixel-label {
	font-size: 20rpx;
	color: #fff;
}
.binance-header-pixel-value {
	font-size: 24rpx;
	font-weight: bold;
	color: #fff;
}
.binance-header-pixel-divider {
	height: 8rpx;
	margin-top: 18rpx;
	margin-bottom: 0;
	background: linear-gradient(to right, #fff 60rpx, #181A20 0%);
	border-radius: 4rpx;
}
.period-popup-mask {
 
	position: fixed;
	left: 0; right: 0; bottom: 0; top: 0;
	background: rgba(0,0,0,0.3);
	z-index: 9999;
	display: flex;
	align-items: flex-end;
	justify-content: center;
}
.period-popup {
	width: 100vw;
	background: #181A20;
	border-radius: 24rpx 24rpx 0 0;
	padding: 32rpx 0 24rpx 0;
	box-shadow: 0 -4rpx 24rpx #eee;
}
.period-popup-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 0 32rpx;
	margin-bottom: 24rpx;
}
.period-popup-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #fff;
	flex: 1;
	text-align: center;
}
.period-popup-close {
	width: 60rpx;
	height: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: 50%;
	background: rgba(255, 255, 255, 0.1);
	cursor: pointer;
	transition: background 0.2s;
}
.period-popup-close:hover {
	background: rgba(255, 255, 255, 0.2);
}
.period-popup-grid {
	display: flex;
	flex-wrap: wrap;
	justify-content: center;
	gap: 18rpx 18rpx;
	padding: 0 24rpx;
}
.period-popup-btn {
	width: 120rpx;
	height: 60rpx;
	font-size: 26rpx;
	border-radius: 16rpx;
	background: #fff;
	color: #000;
	border: 1rpx solid #eee;
	margin-bottom: 8rpx;
	transition: all 0.2s;
}
.period-popup-btn:active {
	background: #fff;
	color: #FF5B3A;
	border: 1rpx solid #FF5B3A;
}
.period-popup-btn.active {
	background: #FF5B3A !important;
	color: #fff !important;
	border: 1rpx solid #FF5B3A !important;
	font-weight: bold;
}
.period-popup-tips {
	text-align: center;
	color: #999;
	font-size: 22rpx;
	margin-top: 24rpx;
}
.custom-navbar {
	width: 100vw;
	background: #121212 !important;
	box-shadow: none !important;
	border-bottom: none !important;
	height: 48px;
	display: flex;
	align-items: center;
	justify-content: center;
}
.navbar-content {
	width: 100%;
	display: flex;
	align-items: center;
	justify-content: space-between;
	height: 48px;
	padding: 0 12px;
}
.left-area {
	width: 40px;
	display: flex;
	align-items: center;
	justify-content: flex-start;
}
.center-area {
	flex: 1;
	display: flex;
	align-items: center;
	justify-content: center;
}
.page-title {
	font-size: 16px;
	font-weight: bold;
	color: #fff;
	letter-spacing: 1px;
}
.navbar-arrow {
	font-size: 18px;
	color: #fff;
	margin-left: 2px;
	font-weight: bold;
}
.right-area {
	width: 60px;
	display: flex;
	align-items: center;
	justify-content: flex-end;
	gap: 10px;
}
.navbar-star {
	font-size: 20px;
	color: #fff;
	font-weight: bold;
}
.navbar-menu {
	font-size: 20px;
	color: #FFD700;
	/* font-weight: bold; */
}
.pair-area {
	display: flex;
	align-items: center;
	margin-left: 4px;
}
.navbar-time {
	color: #fff;
	font-size: 16px;
	font-weight: 500;
	margin-right: 2px;
}
.bottom-action-bar {
	position: fixed;
	left: 0;
	right: 0;
	bottom: 0;
	width: 100vw;
	background: #fff;
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 10px 12px 10px 12px;
	z-index: 999;
	gap: 14px;
	box-shadow: 0 -2px 8px #eee;
}
.buy-up-btn, .buy-down-btn {
	flex: 1;
	height: 40px;
	line-height: 40px;
	font-size: 16px;
	border-radius: 6px;
	border: none;
	color: #fff;
	display: flex;
	align-items: center;
	justify-content: center;
	font-weight: bold;
	cursor: pointer;
}
.buy-up-btn {
	background: #02BF87;
}
.buy-down-btn {
	background: #F34A69;
}
.btn-icon {
	font-size: 20px;
	margin-right: 7px;	 
}
.profit-section {
  background: #181A20;
  border-radius: 12px;
  margin: 5px 12px 24px 12px;
  box-shadow: 0 2px 12px #00000022;
  padding: 0 0 8px 0;
  overflow: hidden;
}
.profit-tabs {
  display: flex;
  align-items: center;
  background: #181A20;
  /* border-bottom: 1px solid #fff; */
  padding: 0 18px;
  margin-bottom: 0;
}
.profit-tab {
  font-size: 16px;
  color: #fff;
  margin-right: 24px;
  padding: 14px 0 10px 0;
  position: relative;
  cursor: pointer;
  background: transparent;
  transition: background 0.2s, color 0.2s;
}
.profit-tab.active {
  color: #000;
  background: #FFD700;
  font-weight: bold;
  border-bottom: 2px solid #FFD700;
  border-radius: 8px 8px 0 0;
}
.profit-dot {
  display: none;
}
.profit-table {
  padding: 0 18px 8px 18px;
}
.profit-table-header {
  display: flex;
  justify-content: space-between;
  font-size: 15px;
  color: #FFD700;
  font-weight: bold;
  background: #181A20;
  border-radius: 0;
  padding: 10px 0 10px 0;
  margin-bottom: 0;
  border-bottom: 1px solid #FFD700;
}
.profit-table-row {
  display: flex;
  justify-content: space-between;
  font-size: 15px;
  color: #fff;
  padding: 10px 0;
  border-bottom: 1px solid #222;
  background: #181A20;
}
.profit-table-row:last-child {
  border-bottom: none;
}
.profit-green {
  color: #02BF87;
}
.profit-red {
  color: #F34A69;
}
.main-scroll-area {
  flex: 1;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  padding-bottom: 70px;
  background: #121212 !important;
}
/* 完全对齐合约页面tab、表格、按钮样式 */
.futures-tabs {
  margin: 18rpx 24rpx 0 24rpx;
  background: #181818;
  border-radius: 10rpx;
  border: 1px solid #FFD70033;
  padding: 0 0 10rpx 0;
  box-shadow: 0 2rpx 8rpx #FFD70011;
}
.tab-list {
  display: flex;
  border-bottom: 1px solid #FFD70022;
}
.tab {
  flex: 1;
  text-align: center;
  font-size: 25rpx;
  color: #fff;
  padding: 12rpx 0 8rpx 0;
  font-weight: 600;
  cursor: pointer;
  letter-spacing: 0.5px;
  background: transparent;
  border-radius: 0;
  transition: background 0.2s, color 0.2s;
}
.tab.active {
  color: #111;
  background: linear-gradient(90deg, #FFD700 0%, #FFEA70 100%);
  border-radius: 10rpx 10rpx 0 0;
  box-shadow: 0 1rpx 4rpx #FFD70033;
}
.tab-table {
  min-height: 600rpx;
  overflow-y: auto;
}
.table-header, .table-row {
  display: flex;
  align-items: center;
  min-height: 40rpx;
  height: 48rpx;
}
.table-header text, .table-row text {
  flex: 1 1 0;
  min-width: 0;
  text-align: center;
  line-height: 48rpx;
  font-size: 24rpx;
}
.table-header {
  font-weight: bold;
  color: #fff;
  background: #181818;
  padding-top: 15rpx;
  padding-bottom: 15rpx;
}
.table-row {
  color: #fff;
  border-bottom: 1px solid #FFD70011;
  min-height: 32rpx;
  align-items: center;
}
.table-row:last-child {
  border-bottom: none;
}
.table-more {
  display: flex;
  justify-content: center;
  margin: 10rpx 0;
}
.table-more button {
  background: transparent;
  color: #fff;
  font-size: 22rpx;
  font-weight: bold;
  border-radius: 16rpx;
  padding: 0 32rpx;
  height: 40rpx;
  line-height: 40rpx;
  border: none;
  box-shadow: none;
  transition: none;
  min-width: 120rpx;
}
.table-more button:active {
  background: transparent;
  color: #fff;
  box-shadow: none;
}
.table-more button:disabled {
  background: transparent;
  color: #999;
  box-shadow: none;
}
.hold-card-list {
  display: flex;
  flex-direction: column;
  gap: 18rpx;
  padding: 18rpx 12rpx 18rpx 12rpx;
}
.hold-card {
  background: #18191D !important;
  border-radius: 14rpx;
  border: 1px solid #FFD70033;
  box-shadow: 0 2rpx 8rpx #FFD70011;
  padding: 18rpx 20rpx;
  margin: 0 6rpx;
}
.hold-row-2col {
  display: flex;
  flex-direction: row;
  margin-bottom: 8rpx;
}
.hold-col {
  flex: 1;
  display: flex;
  align-items: center;
}
.hold-label {
  color: #FFD700;
  width: 70rpx;
  font-size: 22rpx;
  font-weight: 600;
}
.hold-value {
  color: #fff;
  font-size: 22rpx;
  margin-left: 12rpx;
  word-break: break-all;
}
.red { color: #F34A69; }
.green { color: #02BF87; }
.order-popup-mask {
  /* 移除 border-radius，保持全屏遮罩 */
  position: fixed;
  left: 0; right: 0; top: 0; bottom: 0;
  background: rgba(0,0,0,0.35);
  z-index: 9999;
  display: flex;
  align-items: flex-end;
  justify-content: center;
}
.order-popup {
  background: #18191D !important;
  border-radius: 24rpx 24rpx 0 0;
  padding: 24px 18px 18px 18px;
  box-shadow: 0 4px 24px rgba(0,0,0,0.25);
  color: #fff;
  width: 95vw;
  max-width: 500px;
  margin: 0 auto;
}
.order-period-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 18px;
  gap: 12px;
}
.order-period-btn {
  flex: 1;
  text-align: center;
  height: 32px;
  line-height: 32px;
  padding: 0;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  background: #fff;
  color: #111;
  border: none;
  transition: all 0.2s;
  cursor: pointer;
}
.order-period-btn.active {
  background: #FFD700;
  color: #111;
}
.order-popup-info {
  margin-bottom: 18px;
}
.order-popup-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}
.order-popup-label {
	width: 100px;
  color: #fff;
  font-size: 12px;
}
.order-popup-value {
  color: #fff;
  font-weight: bold;
  font-size: 13px;
}
.order-popup-input {
  width: 100%;
  background: #1E1E1E !important;
  border: none !important;
  border-radius: 8px;
  color: #eee;
  font-size: 13px;
  padding: 8px 12px;
  margin-top: 6px;
  box-sizing: border-box;
  height: 32px;
  outline: none;
}
.order-popup-input::placeholder {
  color: #888;
  opacity: 1;
  font-weight: 400;
}
.order-confirm-btn {
  width: 100%;
  margin-top: 18px;
  padding: 4px 0;
  border-radius: 4px;
  font-size: 13px;
  font-weight: bold;
  border: none;
  color: #fff;
  background: #02BF87;
  transition: background 0.2s;
  cursor: pointer;
  /* height: 32px; */
  line-height: 32px;
}
.order-confirm-btn.down {
  background: #F34A69;
}
.skeleton-chart {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, #1a1a1a 25%, #2a2a2a 50%, #1a1a1a 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 12rpx;
  z-index: 10; /* 确保在图表上方 */
  pointer-events: none; /* 允许触摸事件穿透到下层图表 */
}

.skeleton-chart::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 40px;
  height: 40px;
  border: 3px solid #333;
  border-top: 3px solid #FFD700;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.skeleton-header-pixel {
  width: 100%;
  height: 90px;
  background: linear-gradient(90deg, #1a1a1a 25%, #2a2a2a 50%, #1a1a1a 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 24rpx 24rpx 0 0;
  position: relative;
}

@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

@keyframes spin {
  0% { transform: translate(-50%, -50%) rotate(0deg); }
  100% { transform: translate(-50%, -50%) rotate(360deg); }
}

.chart-loading-text {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  text-align: center;
  z-index: 2;
}

.loading-title {
  display: block;
  color: #FFD700;
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 4px;
}

.loading-subtitle {
  display: block;
  color: #999;
  font-size: 14px;
}

.header-loading-content {
  padding: 20px 15px;
}

.skeleton-price-line {
  width: 60%;
  height: 32px;
  background: linear-gradient(90deg, #2a2a2a 25%, #3a3a3a 50%, #2a2a2a 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 6px;
  margin-bottom: 15px;
}

.skeleton-info-grid {
  display: flex;
  justify-content: space-between;
  gap: 10px;
}

.skeleton-info-item {
  flex: 1;
  height: 20px;
  background: linear-gradient(90deg, #2a2a2a 25%, #3a3a3a 50%, #2a2a2a 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 4px;
}
.futures-tabs .tab-list .tab.active {
  background: #fff !important;
  color: #111 !important;
  font-weight: bold;
}

/* 空状态样式 */
.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200rpx;
  flex-direction: column;
}

.empty-text {
  color: #999;
  font-size: 28rpx;
  text-align: center;
}
</style>
