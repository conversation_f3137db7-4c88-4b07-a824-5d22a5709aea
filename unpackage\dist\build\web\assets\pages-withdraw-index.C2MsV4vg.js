import{H as a,n as e,e as t,s,f as l,h as i,w as o,i as n,o as d,j as r,m as c,z as u,t as m,k as h,x as f,I as w,Y as p,y as g}from"./index-DSmyjKbQ.js";import{_}from"./uni-icons.ouCj1myJ.js";import{r as b}from"./uni-app.es.CZvmdujn.js";import{r as y}from"./request.DeqggKcp.js";import{_ as T}from"./_plugin-vue_export-helper.BCo6x5W8.js";import"./index.B6QF5Ba_.js";const P=T({data:()=>({statusBarHeight:0,navBarHeight:50,navPaddingTop:44,address:"",accountTypes:["BEP20","TRC20"],accountTypeIndex:0,amount:"",email:"",payPwd:"",remark:"",withdrawParams:{minWithdraw:0,maxWithdraw:0,withdrawFee:0,enableWithdraw:!0},fee:0,showConfirmDialog:!1,availableBalance:0}),computed:{withdrawAmountPlaceholder(){return`单笔最小提现：大于${this.withdrawParams.minWithdraw}USDT`},withdrawDesc(){return`单笔最小提现大于${this.withdrawParams.minWithdraw}USDT，单笔最大提现${this.withdrawParams.maxWithdraw}USDT。`}},watch:{amount(a){this.calcFee()}},created(){const e=a();this.statusBarHeight=e.statusBarHeight,this.navPaddingTop=this.statusBarHeight+this.navBarHeight,this.getWithdrawParams(),this.getUserInfo()},methods:{handleBack(){e()},goToRecord(){t({url:"/pages/withdraw/record"})},sendCode(){s({title:"验证码已发送",icon:"none"})},async getWithdrawParams(){try{const a=await y({url:"/api/sys/params/transfer-withdraw",method:"GET"});200===a.code&&a.data&&(this.withdrawParams=a.data)}catch(a){s({title:"获取参数失败",icon:"none"})}},async getUserInfo(){try{const a=await y({url:"/api/user/info",method:"GET"});200===a.code&&a.data&&(this.availableBalance=a.data.availableBalance)}catch(a){s({title:"获取余额失败",icon:"none"})}},calcFee(){const a=Number(this.amount),e=Number(this.withdrawParams.withdrawFee||0);isNaN(a)||isNaN(e)?this.fee=0:this.fee=e},submit(){this.address?this.amount?this.payPwd?this.withdrawParams.enableWithdraw?Number(this.amount)<Number(this.withdrawParams.minWithdraw)?s({title:`最小提现金额为${this.withdrawParams.minWithdraw}`,icon:"none"}):Number(this.amount)>Number(this.withdrawParams.maxWithdraw)?s({title:`最大提现金额为${this.withdrawParams.maxWithdraw}`,icon:"none"}):Number(this.amount)<=Number(this.fee)?s({title:"金额需大于手续费",icon:"none"}):this.showConfirmDialog=!0:s({title:"当前不允许提现",icon:"none"}):s({title:"请输入支付密码",icon:"none"}):s({title:"请输入提现数量",icon:"none"}):s({title:"请输入提现地址",icon:"none"})},confirmWithdraw(){y({url:"/api/withdraw/create",method:"POST",data:{amount:this.amount,address:this.address,chainName:this.accountTypes[this.accountTypeIndex],remark:this.remark,securityPassword:this.payPwd}}).then((a=>{200===a.code?(s({title:"提现申请已提交",icon:"success"}),this.amount="",this.address="",this.remark="",this.payPwd="",this.getUserInfo()):s({title:a.msg||"提现失败",icon:"none"}),this.showConfirmDialog=!1})).catch((a=>{s({title:a.message,icon:"none"}),this.showConfirmDialog=!1}))},onAccountTypeChange(a){1===a.detail.value&&s({title:"TRC20提现功能开发中",icon:"none"})}}},[["render",function(a,e,t,s,y,T){const P=b(l("uni-icons"),_),C=n,k=f,x=w,D=p,v=g;return d(),i(C,{class:"container"},{default:o((()=>[r(C,{class:"custom-navbar",style:u({paddingTop:y.statusBarHeight+"px"})},{default:o((()=>[r(C,{class:"navbar-content"},{default:o((()=>[r(C,{class:"left-area",onClick:T.handleBack},{default:o((()=>[r(P,{type:"left",size:"20",color:"#fff"})])),_:1},8,["onClick"]),r(k,{class:"page-title"},{default:o((()=>[c("提现")])),_:1}),r(C,{class:"right-area"},{default:o((()=>[r(k,{class:"record-link",onClick:T.goToRecord},{default:o((()=>[c("提现记录")])),_:1},8,["onClick"])])),_:1})])),_:1})])),_:1},8,["style"]),r(C,{class:"scroll-content",style:u({paddingTop:y.navPaddingTop+"px"})},{default:o((()=>[r(C,{class:"card"},{default:o((()=>[r(C,{class:"form-section"},{default:o((()=>[r(C,{class:"form-item"},{default:o((()=>[r(k,{class:"label"},{default:o((()=>[c("提现地址")])),_:1}),r(x,{class:"input",placeholder:"请输入提现地址",modelValue:y.address,"onUpdate:modelValue":e[0]||(e[0]=a=>y.address=a)},null,8,["modelValue"])])),_:1}),r(C,{class:"form-item"},{default:o((()=>[r(k,{class:"label"},{default:o((()=>[c("账户类型")])),_:1}),r(D,{range:y.accountTypes,value:y.accountTypeIndex,onChange:T.onAccountTypeChange},{default:o((()=>[r(C,{class:"input select"},{default:o((()=>[c(m(y.accountTypes[y.accountTypeIndex]),1)])),_:1})])),_:1},8,["range","value","onChange"])])),_:1}),r(C,{class:"form-item"},{default:o((()=>[r(C,{class:"row-between"},{default:o((()=>[r(k,{class:"label"},{default:o((()=>[c("提现数量")])),_:1}),r(C,{class:"available-text"},{default:o((()=>[c("可用："+m(y.availableBalance)+" USDT",1)])),_:1})])),_:1}),r(x,{class:"input",placeholder:T.withdrawAmountPlaceholder,modelValue:y.amount,"onUpdate:modelValue":e[1]||(e[1]=a=>y.amount=a),type:"number"},null,8,["placeholder","modelValue"])])),_:1}),r(C,{class:"form-item"},{default:o((()=>[r(k,{class:"label"},{default:o((()=>[c("支付密码")])),_:1}),r(x,{class:"input",placeholder:"请输入支付密码",modelValue:y.payPwd,"onUpdate:modelValue":e[2]||(e[2]=a=>y.payPwd=a),password:""},null,8,["modelValue"])])),_:1}),r(C,{class:"form-item"},{default:o((()=>[r(k,{class:"label"},{default:o((()=>[c("备注")])),_:1}),r(x,{class:"input",placeholder:"选填，最多20字",modelValue:y.remark,"onUpdate:modelValue":e[3]||(e[3]=a=>y.remark=a),maxlength:"20"},null,8,["modelValue"])])),_:1})])),_:1}),r(C,{class:"desc-section"},{default:o((()=>[r(k,{class:"desc-title"},{default:o((()=>[c("提现说明：")])),_:1}),r(k,{class:"desc-text"},{default:o((()=>[c("1、由于区块网络匿名和私密，一旦输入错误资讯，您将损失对应资产。请务必仔细核对您的充值及提币地址与数量\\n2、"+m(T.withdrawDesc),1)])),_:1})])),_:1}),r(C,{class:"result-action-row"},{default:o((()=>[r(C,{class:"result-info"},{default:o((()=>[r(k,{class:"result"},{default:o((()=>[c("到账数量："+m(y.amount&&null!==y.fee?(Number(y.amount)-Number(y.fee)).toFixed(2):0)+"USDT",1)])),_:1}),r(k,{class:"result"},{default:o((()=>[c("网络手续费："+m(y.fee)+"USDT",1)])),_:1})])),_:1}),r(v,{class:"submit-btn",onClick:T.submit},{default:o((()=>[c("提现")])),_:1},8,["onClick"])])),_:1})])),_:1})])),_:1},8,["style"]),y.showConfirmDialog?(d(),i(C,{key:0,class:"custom-dialog-mask"},{default:o((()=>[r(C,{class:"custom-dialog"},{default:o((()=>[r(C,{class:"dialog-title"},{default:o((()=>[c("请确认提现信息")])),_:1}),r(C,{class:"dialog-row"},{default:o((()=>[c("提现地址："+m(y.address),1)])),_:1}),r(C,{class:"dialog-row"},{default:o((()=>[c("提现数量："+m(y.amount)+" USDT",1)])),_:1}),r(C,{class:"dialog-row"},{default:o((()=>[c("到账数量："+m(y.amount&&null!==y.fee?(Number(y.amount)-Number(y.fee)).toFixed(2):0)+" USDT",1)])),_:1}),r(C,{class:"dialog-row"},{default:o((()=>[c("手续费："+m(y.fee)+" USDT",1)])),_:1}),r(C,{class:"dialog-actions"},{default:o((()=>[r(v,{class:"dialog-btn cancel",onClick:e[4]||(e[4]=a=>y.showConfirmDialog=!1)},{default:o((()=>[c("取消")])),_:1}),r(v,{class:"dialog-btn confirm",onClick:T.confirmWithdraw},{default:o((()=>[c("确认提现")])),_:1},8,["onClick"])])),_:1})])),_:1})])),_:1})):h("",!0)])),_:1})}],["__scopeId","data-v-8a97f329"]]);export{P as default};
