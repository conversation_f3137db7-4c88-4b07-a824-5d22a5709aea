import{c as t,g as a,f as o,h as e,w as s,i as l,o as i,j as r,m as d,z as c,p as n,t as f,l as u,F as h,q as y,k as m,x as _,y as p}from"./index-C9YaP0ep.js";import{_ as g}from"./uni-icons.CxVA1Ran.js";import{r as S}from"./uni-app.es.CncftGS-.js";import{c as b}from"./index.B6QF5Ba_.js";import{r as P}from"./request.BgmJQJgX.js";import{_ as T}from"./_plugin-vue_export-helper.BCo6x5W8.js";const L=T({name:"MyCopy",data:()=>({baseURL:b.apiBaseUrl,tabIndex:0,balance:0,totalProfit:0,unrealizedProfit:0,todayList:[],todayPage:1,todayPageSize:10,todayTotal:0,todayLoading:!1,todayFinished:!1,historyList:[],historyPage:1,historyPageSize:10,historyTotal:0,historyLoading:!1,historyFinished:!1,statusBarHeight:0,holdProfitSSE:null}),onShow(){console.log("mycopy页面显示，开始加载数据..."),this.loadUserInfo(),this.loadProfitSummary(),this.loadOrderList("today"),this.loadOrderList("history"),this.initHoldProfitSSE()},onHide(){this.holdProfitSSE&&(this.holdProfitSSE.close(),this.holdProfitSSE=null)},methods:{handleBack(){t({url:"/pages/robot/index"})},async loadUserInfo(){var t;const a=await P({url:"/api/user/info",method:"GET"});this.balance=(null==(t=null==a?void 0:a.data)?void 0:t.copyTradeBalance)||0},async loadProfitSummary(){var t;const a=await P({url:"/api/copy/profit/summary",method:"GET"});this.totalProfit=(null==(t=null==a?void 0:a.data)?void 0:t.total_profit)||0},async loadOrderList(t,a=1,o=!1){var e,s;const l="today"===t?this.todayPageSize:this.historyPageSize;"today"===t?this.todayLoading=!0:this.historyLoading=!0;const i=await P({url:"/api/copy/order/list",method:"GET",data:{type:t,page:a,pageSize:l}}),r=(null==(e=null==i?void 0:i.data)?void 0:e.list)||[],d=(null==(s=null==i?void 0:i.data)?void 0:s.total)||0;console.log(`加载${t}订单列表:`,r),console.log("订单ID列表:",r.map((t=>t.id))),"today"===t?(console.log(`当前持仓数据${r.length}条`),this.todayTotal=d,this.todayPage=a,this.todayList=o?this.todayList.concat(r):r,this.todayFinished=this.todayList.length>=d,this.todayLoading=!1,console.log("当前持仓列表更新后:",this.todayList.map((t=>({id:t.id,symbol:t.symbol,status:t.status}))))):(this.historyTotal=d,this.historyPage=a,this.historyList=o?this.historyList.concat(r):r,this.historyFinished=this.historyList.length>=d,this.historyLoading=!1)},formatDate(t){if(!t)return"-";const a="string"==typeof t?new Date(t):t;if(isNaN(a.getTime()))return"-";return`${a.getFullYear()}-${(a.getMonth()+1).toString().padStart(2,"0")}-${a.getDate().toString().padStart(2,"0")} ${a.getHours().toString().padStart(2,"0")}:${a.getMinutes().toString().padStart(2,"0")}:${a.getSeconds().toString().padStart(2,"0")}`},formatSymbol:t=>t?t.replace("/USDT","").replace("USDT",""):"--",getRealTimeProfit:t=>void 0!==t.realTimeProfit?Number(t.realTimeProfit).toFixed(4):Number(t.profit||0).toFixed(4),getRealTimeProfitRate:t=>void 0!==t.realTimeProfitRate?Number(t.realTimeProfitRate).toFixed(3)+"%":Number(t.profitRate||0).toFixed(3)+"%",getRealTimeProfitRateValue:t=>void 0!==t.realTimeProfitRate?Number(t.realTimeProfitRate):Number(t.profitRate||0),getTotalUnrealizedProfit(){let t=0;return this.todayList.forEach((a=>{1===a.status&&(t+=Number(void 0!==a.realTimeProfit?a.realTimeProfit:a.profit||0))})),this.historyList.forEach((a=>{1===a.status&&(t+=Number(void 0!==a.realTimeProfit?a.realTimeProfit:a.profit||0))})),t},getTotalUnrealizedProfitRaido(){let t=0;return this.todayList.forEach((a=>{1===a.status&&(t+=Number(void 0!==a.profitRate?a.profitRate:a.profitRate||0))})),this.historyList.forEach((a=>{1===a.status&&(t+=Number(void 0!==a.profitRate?a.profitRate:a.profitRate||0))})),t},initHoldProfitSSE(){this.holdProfitSSE&&this.holdProfitSSE.close();const t=a("token")||"";if(!t)return void console.log("没有token，无法建立SSE连接");const o=this.baseURL+"/api/copy/order/hold/profit/stream?token="+encodeURIComponent(t);console.log("建立SSE连接:",o),this.holdProfitSSE=new window.EventSource(o),this.holdProfitSSE.onopen=()=>{console.log("SSE连接已建立")},this.holdProfitSSE.onerror=t=>{console.error("SSE连接错误:",t)},this.holdProfitSSE.addEventListener("profit-update",(async t=>{console.log("收到SSE原始事件:",t);let a=t.data;console.log("SSE原始数据:",a),a.startsWith("data: ")&&(a=a.slice(6)),console.log("处理后的数据:",a);try{const t=JSON.parse(a);console.log("解析后的持仓盈利数据:",t),console.log("当前持仓列表:",this.todayList.map((t=>({id:t.id,symbol:t.symbol})))),console.log("当前历史列表:",this.historyList.map((t=>({id:t.id,symbol:t.symbol}))));let o=!1,e=0,s=!1,l=!1;t.forEach((t=>{console.log("处理订单数据:",t),2===t.status&&(l=!0);const a=this.todayList.findIndex((a=>a.id===t.orderId));console.log(`在持仓列表中查找订单${t.orderId}，索引:`,a),-1!==a&&(1===t.status?(console.log(`更新持仓订单${t.orderId}的实时盈利:`,t.profit),this.todayList[a].realTimeProfit=t.profit,this.todayList[a].realTimeProfitRate=t.profitRate,this.todayList[a].currentPrice=t.currentPrice,this.todayList[a].status=t.status,s=!0):(this.todayList.splice(a,1),o=!0,e++,console.log(`订单${t.orderId}状态变为${t.status}，从持仓列表中移除`)));const i=this.historyList.findIndex((a=>a.id===t.orderId));console.log(`在历史列表中查找订单${t.orderId}，索引:`,i),-1!==i&&(1===t.status?(console.log(`更新历史订单${t.orderId}的实时盈利:`,t.profit),this.historyList[i].realTimeProfit=t.profit,this.historyList[i].realTimeProfitRate=t.profitRate,this.historyList[i].currentPrice=t.currentPrice,this.historyList[i].status=t.status,s=!0):(console.log(`历史订单${t.orderId}状态变为${t.status}，更新状态`),this.historyList[i].status=t.status,this.historyList[i].realTimeProfit=void 0,this.historyList[i].realTimeProfitRate=void 0,s=!0))})),s&&(console.log("有数据更新，强制刷新页面显示"),this.$forceUpdate()),o&&(console.log(`共移除${e}个非持仓订单`),await this.loadUserInfo(),await this.loadProfitSummary()),l&&(console.log("检测到已平仓订单，刷新历史跟单数据"),this.historyPage=1,this.historyFinished=!1,await this.loadOrderList("history",1,!1))}catch(o){console.error("处理实时盈利数据失败:",o)}})),this.holdProfitSSE.onerror=()=>{this.holdProfitSSE.close(),setTimeout((()=>this.initHoldProfitSSE()),5e3)}}}},[["render",function(t,a,b,P,T,L){const v=S(o("uni-icons"),g),R=l,E=_,k=p;return i(),e(R,{class:"mycopy-container"},{default:s((()=>[r(R,{class:"custom-navbar",style:c({paddingTop:T.statusBarHeight+"px"})},{default:s((()=>[r(R,{class:"navbar-content"},{default:s((()=>[r(R,{class:"left-area",onClick:L.handleBack},{default:s((()=>[r(v,{type:"left",size:"20",color:"#fff"})])),_:1},8,["onClick"]),r(E,{class:"page-title"},{default:s((()=>[d("我的跟单")])),_:1}),r(R,{class:"right-area"})])),_:1})])),_:1},8,["style"]),r(R,{class:"mycopy-top-card"},{default:s((()=>[r(R,{class:"top-row"},{default:s((()=>[r(R,{class:"top-label"},{default:s((()=>[d("未实现盈亏")])),_:1}),r(R,{class:n(["top-value",L.getTotalUnrealizedProfit()>0?"profit":L.getTotalUnrealizedProfit()<0?"loss":""])},{default:s((()=>[d(f(L.getTotalUnrealizedProfit().toFixed(3))+" USDT",1)])),_:1},8,["class"])])),_:1}),r(R,{class:"bottom-row"},{default:s((()=>[r(R,{class:"bottom-col"},{default:s((()=>[r(R,{class:"bottom-label"},{default:s((()=>[d("跟单净利润")])),_:1}),r(R,{class:"bottom-value"},{default:s((()=>[d(f((Number(T.totalProfit)||0).toFixed(3))+" USDT",1)])),_:1})])),_:1}),r(R,{class:"bottom-col"},{default:s((()=>[r(R,{class:"bottom-label"},{default:s((()=>[d("可用余额")])),_:1}),r(R,{class:"bottom-value"},{default:s((()=>[d(f((Number(T.balance)||0).toFixed(3))+" USDT",1)])),_:1})])),_:1})])),_:1})])),_:1}),r(R,{class:"mycopy-tabs"},{default:s((()=>[r(R,{class:n(["mycopy-tab",{active:0===T.tabIndex}]),onClick:a[0]||(a[0]=t=>T.tabIndex=0)},{default:s((()=>[d("当前持仓")])),_:1},8,["class"]),r(R,{class:n(["mycopy-tab",{active:1===T.tabIndex}]),onClick:a[1]||(a[1]=t=>T.tabIndex=1)},{default:s((()=>[d("历史跟单")])),_:1},8,["class"])])),_:1}),r(R,{class:"mycopy-tab-content"},{default:s((()=>[0===T.tabIndex?(i(),u(h,{key:0},[0===T.todayList.length?(i(),e(R,{key:0,class:"mycopy-empty"},{default:s((()=>[d("暂无数据")])),_:1})):(i(),e(R,{key:1},{default:s((()=>[(i(!0),u(h,null,y(T.todayList,(t=>(i(),e(R,{key:t.id,class:"order-card"},{default:s((()=>[r(R,{class:"order-header"},{default:s((()=>[r(R,{class:"symbol-info"},{default:s((()=>[r(E,{class:"symbol"},{default:s((()=>[d(f(L.formatSymbol(t.symbol)),1)])),_:2},1024),r(E,{class:n(["direction",1===t.direction?"direction-up":"direction-down"])},{default:s((()=>[d(f(1===t.direction?"买涨":"买跌"),1)])),_:2},1032,["class"])])),_:2},1024),r(E,{class:n(["status",1==t.status?"status-holding":"status-closed"])},{default:s((()=>[d(f(1==t.status?"持仓中":"已平仓"),1)])),_:2},1032,["class"])])),_:2},1024),r(R,{class:"order-main-info"},{default:s((()=>[r(R,{class:"order-row-horizontal"},{default:s((()=>[r(R,{class:"order-item"},{default:s((()=>[r(E,{class:"label"},{default:s((()=>[d("持仓("+f(t.symbol?t.symbol.split("/")[0]:"")+")",1)])),_:2},1024),r(E,{class:"value"},{default:s((()=>[d(f(t.positionAmount),1)])),_:2},1024)])),_:2},1024),r(R,{class:"order-item"},{default:s((()=>[r(E,{class:"label"},{default:s((()=>[d("盈利率")])),_:1}),r(E,{class:n(["value",L.getRealTimeProfitRateValue(t)>0?"profit":L.getRealTimeProfitRateValue(t)<0?"loss":""])},{default:s((()=>[d(f(L.getRealTimeProfitRate(t)),1)])),_:2},1032,["class"])])),_:2},1024),r(R,{class:"order-item"},{default:s((()=>[r(E,{class:"label"},{default:s((()=>[d("杠杆")])),_:1}),r(E,{class:"value"},{default:s((()=>[d(f(t.lever)+"x",1)])),_:2},1024)])),_:2},1024)])),_:2},1024),r(R,{class:"order-row-horizontal"},{default:s((()=>[r(R,{class:"order-item"},{default:s((()=>[r(E,{class:"label"},{default:s((()=>[d("保证金(USDT)")])),_:1}),r(E,{class:"value"},{default:s((()=>[d(f(t.marginAmount),1)])),_:2},1024)])),_:2},1024),r(R,{class:"order-item"},{default:s((()=>[r(E,{class:"label"},{default:s((()=>[d("开仓价(USDT)")])),_:1}),r(E,{class:"value"},{default:s((()=>[d(f(t.openPrice),1)])),_:2},1024)])),_:2},1024),r(R,{class:"order-item"})])),_:2},1024)])),_:2},1024),r(R,{class:"order-footer"},{default:s((()=>[r(R,{class:"order-row"},{default:s((()=>[r(E,{class:"label"},{default:s((()=>[d("开仓")])),_:1}),r(E,{class:"value"},{default:s((()=>[d(f(L.formatDate(t.openTime)),1)])),_:2},1024)])),_:2},1024),t.closeTime?(i(),e(R,{key:0,class:"order-row"},{default:s((()=>[r(E,{class:"label"},{default:s((()=>[d("平仓")])),_:1}),r(E,{class:"value"},{default:s((()=>[d(f(L.formatDate(t.closeTime)),1)])),_:2},1024)])),_:2},1024)):m("",!0)])),_:2},1024)])),_:2},1024)))),128))])),_:1})),T.todayList.length>0?(i(),e(R,{key:2,class:"pagination"},{default:s((()=>[T.todayFinished||T.todayLoading?m("",!0):(i(),e(k,{key:0,onClick:a[2]||(a[2]=t=>L.loadOrderList("today",T.todayPage+1,!0))},{default:s((()=>[d("加载更多")])),_:1})),T.todayFinished?(i(),e(E,{key:1,class:"no-more"},{default:s((()=>[d("没有更多了")])),_:1})):m("",!0),T.todayLoading?(i(),e(E,{key:2,class:"loading"},{default:s((()=>[d("加载中...")])),_:1})):m("",!0)])),_:1})):m("",!0)],64)):(i(),u(h,{key:1},[0===T.historyList.length?(i(),e(R,{key:0,class:"mycopy-empty"},{default:s((()=>[d("暂无数据")])),_:1})):(i(),e(R,{key:1},{default:s((()=>[(i(!0),u(h,null,y(T.historyList,(t=>(i(),e(R,{key:t.id,class:"order-card"},{default:s((()=>[r(R,{class:"order-header"},{default:s((()=>[r(R,{class:"symbol-info"},{default:s((()=>[r(E,{class:"symbol"},{default:s((()=>[d(f(L.formatSymbol(t.symbol)),1)])),_:2},1024),r(E,{class:n(["direction",1===t.direction?"direction-up":"direction-down"])},{default:s((()=>[d(f(1===t.direction?"买涨":"买跌"),1)])),_:2},1032,["class"])])),_:2},1024),r(E,{class:n(["status",1==t.status?"status-holding":"status-closed"])},{default:s((()=>[d(f(1==t.status?"持仓中":"已平仓"),1)])),_:2},1032,["class"])])),_:2},1024),r(R,{class:"order-main-info"},{default:s((()=>[r(R,{class:"order-row-horizontal"},{default:s((()=>[r(R,{class:"order-item"},{default:s((()=>[r(E,{class:"label"},{default:s((()=>[d("持仓("+f(t.symbol?t.symbol.split("/")[0]:"")+")",1)])),_:2},1024),r(E,{class:"value"},{default:s((()=>[d(f(t.positionAmount),1)])),_:2},1024)])),_:2},1024),r(R,{class:"order-item"},{default:s((()=>[r(E,{class:"label"},{default:s((()=>[d("盈利(USDT)")])),_:1}),r(E,{class:n(["value",L.getRealTimeProfit(t)>0?"profit":L.getRealTimeProfit(t)<0?"loss":""])},{default:s((()=>[d(f(L.getRealTimeProfit(t)),1)])),_:2},1032,["class"])])),_:2},1024),r(R,{class:"order-item"},{default:s((()=>[r(E,{class:"label"},{default:s((()=>[d("杠杆")])),_:1}),r(E,{class:"value"},{default:s((()=>[d(f(t.lever)+"x",1)])),_:2},1024)])),_:2},1024)])),_:2},1024),r(R,{class:"order-row-horizontal"},{default:s((()=>[r(R,{class:"order-item"},{default:s((()=>[r(E,{class:"label"},{default:s((()=>[d("保证金(USDT)")])),_:1}),r(E,{class:"value"},{default:s((()=>[d(f(t.marginAmount),1)])),_:2},1024)])),_:2},1024),r(R,{class:"order-item"},{default:s((()=>[r(E,{class:"label"},{default:s((()=>[d("开仓价(USDT)")])),_:1}),r(E,{class:"value"},{default:s((()=>[d(f(t.openPrice),1)])),_:2},1024)])),_:2},1024),r(R,{class:"order-item"},{default:s((()=>[r(E,{class:"label"},{default:s((()=>[d("平仓价(USDT)")])),_:1}),r(E,{class:"value"},{default:s((()=>[d(f(t.closePrice||"--"),1)])),_:2},1024)])),_:2},1024)])),_:2},1024)])),_:2},1024),r(R,{class:"order-footer"},{default:s((()=>[r(R,{class:"order-row"},{default:s((()=>[r(E,{class:"label"},{default:s((()=>[d("开仓")])),_:1}),r(E,{class:"value"},{default:s((()=>[d(f(L.formatDate(t.openTime)),1)])),_:2},1024)])),_:2},1024),t.closeTime?(i(),e(R,{key:0,class:"order-row"},{default:s((()=>[r(E,{class:"label"},{default:s((()=>[d("平仓")])),_:1}),r(E,{class:"value"},{default:s((()=>[d(f(L.formatDate(t.closeTime)),1)])),_:2},1024)])),_:2},1024)):m("",!0)])),_:2},1024)])),_:2},1024)))),128))])),_:1})),T.historyList.length>0?(i(),e(R,{key:2,class:"pagination"},{default:s((()=>[T.historyFinished||T.historyLoading?m("",!0):(i(),e(k,{key:0,onClick:a[3]||(a[3]=t=>L.loadOrderList("history",T.historyPage+1,!0))},{default:s((()=>[d("加载更多")])),_:1})),T.historyFinished?(i(),e(E,{key:1,class:"no-more"},{default:s((()=>[d("没有更多了")])),_:1})):m("",!0),T.historyLoading?(i(),e(E,{key:2,class:"loading"},{default:s((()=>[d("加载中...")])),_:1})):m("",!0)])),_:1})):m("",!0)],64))])),_:1})])),_:1})}],["__scopeId","data-v-f90f70fd"]]);export{L as default};
