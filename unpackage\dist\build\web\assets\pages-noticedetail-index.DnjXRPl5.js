import{H as t,n as e,f as a,h as s,w as i,i as o,o as l,j as c,m as r,z as n,p as d,t as f,x as p,Q as u,R as g}from"./index-C9YaP0ep.js";import{_ as h}from"./uni-icons.CxVA1Ran.js";import{r as m}from"./uni-app.es.CncftGS-.js";import{r as y}from"./request.BgmJQJgX.js";import{_}from"./_plugin-vue_export-helper.BCo6x5W8.js";import"./index.B6QF5Ba_.js";const T=_({data:()=>({statusBarHeight:0,scrollHeight:0,noticeId:"",noticeDetail:{}}),onLoad(e){e.id&&(this.noticeId=e.id,this.getNoticeDetail());const a=t();this.statusBarHeight=a.statusBarHeight,this.scrollHeight=a.windowHeight},methods:{handleBack(){e()},async getNoticeDetail(){try{const t=await y({url:`/api/notice/detail/${this.noticeId}`,method:"GET"});200===t.code&&t.data&&(this.noticeDetail=t.data)}catch(t){console.error("获取公告详情失败:",t)}},formatRichText:t=>t?t=(t=(t=(t=(t=(t=(t=t.replace(/<img/gi,'<img style="max-width:100%;height:auto;display:block;"')).replace(/<video/gi,'<video style="max-width:100%;height:auto;display:block;"')).replace(/color:\s*rgb\(0,\s*0,\s*0\)/gi,"color: #fff")).replace(/color:\s*#000000/gi,"color: #fff")).replace(/color:\s*black/gi,"color: #fff")).replace(/<p>/gi,'<p style="color: #fff;">')).replace(/<span>/gi,'<span style="color: #fff;">'):"",formatTime(t){if(!t)return"";const e=new Date(t);return`${e.getFullYear()}-${String(e.getMonth()+1).padStart(2,"0")}-${String(e.getDate()).padStart(2,"0")} ${String(e.getHours()).padStart(2,"0")}:${String(e.getMinutes()).padStart(2,"0")}`},getNoticeTypeText(t){switch(t){case 1:return"重要";case 2:default:return"通知";case 3:return"系统";case 4:return"活动";case 5:return"维护"}}}},[["render",function(t,e,y,_,T,x){const D=m(a("uni-icons"),h),H=o,w=p,S=u,j=g;return l(),s(H,{class:"notice-detail"},{default:i((()=>[c(H,{class:"custom-navbar",style:n({paddingTop:T.statusBarHeight+"px"})},{default:i((()=>[c(H,{class:"navbar-content"},{default:i((()=>[c(H,{class:"left-area",onClick:x.handleBack},{default:i((()=>[c(D,{type:"left",size:"20",color:"#fff"})])),_:1},8,["onClick"]),c(w,{class:"page-title"},{default:i((()=>[r("公告详情")])),_:1}),c(H,{class:"right-area"})])),_:1})])),_:1},8,["style"]),c(j,{class:"detail-content","scroll-y":"",style:n({height:T.scrollHeight+"px"})},{default:i((()=>[c(H,{class:"notice-header"},{default:i((()=>[c(H,{class:"title-wrap"},{default:i((()=>[c(w,{class:d(["tag",{important:1===T.noticeDetail.noticeType||1===T.noticeDetail.isTop}])},{default:i((()=>[r(f(x.getNoticeTypeText(T.noticeDetail.noticeType)),1)])),_:1},8,["class"]),c(w,{class:"title"},{default:i((()=>[r(f(T.noticeDetail.title),1)])),_:1})])),_:1}),c(w,{class:"time"},{default:i((()=>[r(f(x.formatTime(T.noticeDetail.createTime)),1)])),_:1})])),_:1}),c(H,{class:"rich-content"},{default:i((()=>[c(S,{nodes:x.formatRichText(T.noticeDetail.content)},null,8,["nodes"])])),_:1})])),_:1},8,["style"])])),_:1})}],["__scopeId","data-v-48197d75"]]);export{T as default};
