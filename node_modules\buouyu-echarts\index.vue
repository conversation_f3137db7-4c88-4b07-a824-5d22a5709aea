<template>
	<!-- 新的：接口对其了H5 -->
	<view class="content" :style="{height,width}" >
		<template v-if="option">
			<!-- #ifdef APP-PLUS || H5 -->
			<view @click="echarts.onClick"
				  @touchstart="echarts.touchStart"
				  @touchmove="echarts.touchMove"
				  @touchend="echarts.touchEnd"
				  @mousedown="echarts.mouseDown"
				  @mousemove="echarts.mouseMove"
				  @mouseup="echarts.mouseUp"
				  :rOption="rOption" :change:rOption="echarts.messageChanged"
				  style="height: 100%;width: 100%; cursor: grab; touch-action: pan-x; user-select: none; -webkit-user-select: none;" :prop="option" :change:prop="echarts.updateEcharts" :id="canvasId"></view>
			<!-- #endif -->
			<!-- #ifndef APP-PLUS || H5 -->
			<canvas style="height: 100%;width: 100%;" type="2d" class="ec-canvas" :canvas-id="canvasId" :id="canvasId"
				:ref="canvasId" bindinit="init" @touchstart="ec.disableTouch ? '' : 'touchStart'"
				@touchmove="ec.disableTouch ? '' : 'touchMove'" @touchend="ec.disableTouch ? '' : 'touchEnd'"></canvas>
			<!-- #endif -->
		</template>
		<template v-else>
			<view style="color:red;">你没有传递option属性噢!</view>
		</template>
		
	</view>
</template>

<script>
	// #ifndef APP-PLUS || H5
	import YuCanvas from './yu-canvas';
	import * as echarts from 'echarts';
	// #endif



	let ctx;
	let chart;
	function wrapTouch(event) {
		for (let i = 0; i < event.touches.length; ++i) {
			const touch = event.touches[i];
			touch.offsetX = touch.x;
			touch.offsetY = touch.y;
		}
		return event;
	}
	export default {
		props: {
			canvasId: {
				type: String,
				default: 'ec-canvas'
			},
			ec: {
				type: Object,
				default: () => ({})
			},
			option: {
				type: Object
			},

			width: {
				type: String,
				default: '100%'
			},
			height: {
				type: String,
				default: '500rpx'
			}
		},
		computed: {
			eOption() {
				return JSON.stringify(this.option)
			}
		},
		data() {
			return {
				isUseNewCanvas: true,
				rOption: null,
				// chart: null,
			}
		},

		mounted() {
			// #ifdef APP-PLUS || H5
			this.$nextTick(() => {
				this.rOption = {
					canvasId: this.canvasId,
					...this.option,
				}
			})
			//  #endif
			// #ifndef APP-PLUS || H5

			this.$nextTick(() => {
				echarts.registerPreprocessor(option => {
					if (option && option.series) {
						if (option.series.length > 0) {
							option.series.forEach(series => {
								series.progressive = 0;
							});
						} else if (typeof option.series === 'object') {
							option.series.progressive = 0;
						}
					}
				});
				this.init();
			})
			// #endif

		},
		methods: {
			init(callback) {
				this.initByNewWay(callback);
			},
			initChart(canvas, width, height, dpr) {
				chart = echarts.init(canvas, null, {
					width: width,
					height: height,
					devicePixelRatio: dpr // 像素
				});
				canvas.setChart(chart);
				chart.setOption(this.option);
				return chart;
			},
			initByNewWay(callback) {
				// version >= 2.9.0：使用新的方式初始化
				const query = uni.createSelectorQuery().in(this)
				query
					.select('.ec-canvas')
					.fields({
						node: true,
						size: true,
						context: true
					})
					.exec(res => {
						let targetNode;
						if (res[0].node) {
							//兼容微信小程序，2.7以上版本
							targetNode = res[0].node
						} else {
							//兼容h5
							const parentNode = this.$refs[this.canvasId].$el
							targetNode = parentNode.getElementsByTagName('canvas')[0]
						}
						const canvasDpr = uni.getSystemInfoSync().pixelRatio
						// targetNode = res[0].context._context.canvas

						const canvasNode = targetNode
						const canvasWidth = res[0].width
						const canvasHeight = res[0].height

						const ctx = canvasNode.getContext('2d')

						const canvas = new YuCanvas(ctx, this.canvasId, true, canvasNode)
						echarts.setCanvasCreator(() => {
							return canvas
						})
						if (typeof callback === 'function') {
							this.chart = callback(canvas, canvasWidth, canvasHeight, canvasDpr)
						} else if (this.option) {
							this.initChart(canvas, canvasWidth, canvasHeight, canvasDpr)
							// this.chart = this.ec.onInit(canvas, canvasWidth, canvasHeight, canvasDpr)
						} else {
							this.triggerEvent('init', {
								canvas: canvas,
								width: canvasWidth,
								height: canvasHeight,
								dpr: canvasDpr
							})
						}
					})
			},


			touchStart(e) {
				if (this.chart && e.touches.length > 0) {
					var touch = e.touches[0];
					var handler = this.chart.getZr().handler;
					handler.dispatch('mousedown', {
						zrX: touch.x,
						zrY: touch.y
					});
					handler.dispatch('mousemove', {
						zrX: touch.x,
						zrY: touch.y
					});
					handler.processGesture(wrapTouch(e), 'start');
				}
			},
			setOption(option){
				// #ifndef APP-PLUS || H5
				chart.setOption(option);
				//  #endif
				// #ifdef APP-PLUS || H5
				this.rOption = {...option}
				//  #endif
			},
			touchMove(e) {
				if (this.chart && e.touches.length > 0) {
					var touch = e.touches[0];
					var handler = this.chart.getZr().handler;
					handler.dispatch('mousemove', {
						zrX: touch.x,
						zrY: touch.y
					});
					handler.processGesture(wrapTouch(e), 'change');
				}
			},
			touchEnd(e) {
				if (this.chart) {
					const touch = e.changedTouches ? e.changedTouches[0] : {};
					var handler = this.chart.getZr().handler;
					handler.dispatch('mouseup', {
						zrX: touch.x,
						zrY: touch.y
					});
					handler.dispatch('click', {
						zrX: touch.x,
						zrY: touch.y
					});
					handler.processGesture(wrapTouch(e), 'end');
				}
			},
			
			onViewClick(val) {
				// console.log(val)

			}
		}
	}
</script>



<script module="echarts" lang="renderjs">
	// #ifdef  APP-PLUS || H5
	import * as echarts from 'echarts'



	let myChart
	export default {
		data() {
			return {
				Coption: null,
				CcanvasId: null,
				// chart: null
			}
		},
		mounted() {
			// #ifdef  APP-PLUS || H5
			// this.$nextTick(() => {
			// 	this.initl()
			// })
			// #endif

		},
		methods: {

			initl() {
				// console.log('app&H5')
			},
			initEcharts() {
				// 初始化ECharts，启用移动端支持
				myChart = echarts.init(document.getElementById(this.CcanvasId), null, {
					devicePixelRatio: window.devicePixelRatio || 1,
					renderer: 'canvas',
					useDirtyRect: false
				});

				// 观测更新的数据在 view 层可以直接访问到
				myChart.setOption(this.Coption);

				// 监听dataZoom事件，检测用户是否在浏览历史数据
				myChart.on('dataZoom', (params) => {
					console.log('DataZoom event:', params);
					// 通知Vue组件用户正在浏览历史数据
					// 这里需要通过某种方式通知父组件
				});

				// 确保图表可以响应触摸事件
				const zr = myChart.getZr();
				if (zr) {
					// 启用触摸事件
					zr.configLayer(0, {
						clearColor: 'transparent'
					});
				}

			},
			updateEcharts(newValue, oldValue, ownerInstance, instance) {
				// 监听 service 层数据变更

				myChart.setOption(newValue)
			},
			onClick(event, ownerInstance) {
				// 调用 service 层的方法
				ownerInstance.callMethod('onViewClick', {
					test: 'test'
				})
			},
			touchStart(event, ownerInstance) {
				console.log('H5 touchStart triggered', event);
				if (myChart && event.touches && event.touches.length > 0) {
					// 阻止默认的触摸行为（包括返回手势）
					event.preventDefault();
					event.stopPropagation();

					const touch = event.touches[0];
					const rect = event.currentTarget.getBoundingClientRect();
					const x = touch.clientX - rect.left;
					const y = touch.clientY - rect.top;

					console.log('Touch coordinates:', { x, y, clientX: touch.clientX, clientY: touch.clientY });

					// 通知Vue组件用户开始拖拽
					ownerInstance.callMethod('onUserStartBrowsing');

					// 模拟鼠标事件来触发dataZoom
					const zr = myChart.getZr();
					if (zr && zr.handler) {
						// 创建鼠标按下事件
						const mouseEvent = {
							type: 'mousedown',
							zrX: x,
							zrY: y,
							which: 1,
							button: 0,
							preventDefault: () => {},
							stopPropagation: () => {}
						};
						zr.handler.dispatch('mousedown', mouseEvent);
					}
				}
			},
			touchMove(event, ownerInstance) {
				console.log('H5 touchMove triggered');
				if (myChart && event.touches && event.touches.length > 0) {
					// 强制阻止所有默认行为（包括返回手势和页面滚动）
					event.preventDefault();
					event.stopPropagation();

					const touch = event.touches[0];
					const rect = event.currentTarget.getBoundingClientRect();
					const x = touch.clientX - rect.left;
					const y = touch.clientY - rect.top;

					// 模拟鼠标移动事件来触发dataZoom
					const zr = myChart.getZr();
					if (zr && zr.handler) {
						const mouseEvent = {
							type: 'mousemove',
							zrX: x,
							zrY: y,
							which: 1,
							button: 0,
							preventDefault: () => {},
							stopPropagation: () => {}
						};
						zr.handler.dispatch('mousemove', mouseEvent);
					}
				}
			},
			touchEnd(event, ownerInstance) {
				console.log('H5 touchEnd triggered');
				if (myChart) {
					// 阻止默认行为
					event.preventDefault();
					event.stopPropagation();

					const touch = event.changedTouches ? event.changedTouches[0] : {};
					const rect = event.currentTarget.getBoundingClientRect();
					const x = touch.clientX ? touch.clientX - rect.left : 0;
					const y = touch.clientY ? touch.clientY - rect.top : 0;

					// 模拟鼠标释放事件来触发dataZoom
					const zr = myChart.getZr();
					if (zr && zr.handler) {
						const mouseEvent = {
							type: 'mouseup',
							zrX: x,
							zrY: y,
							which: 1,
							button: 0,
							preventDefault: () => {},
							stopPropagation: () => {}
						};
						zr.handler.dispatch('mouseup', mouseEvent);
					}
				}
			},
			mouseDown(event, ownerInstance) {
				console.log('H5 mouseDown triggered', event);
				if (myChart) {
					const rect = event.currentTarget.getBoundingClientRect();
					const x = event.clientX - rect.left;
					const y = event.clientY - rect.top;

					console.log('Mouse down coordinates:', { x, y });

					// 通知Vue组件用户开始拖拽（可能是浏览历史数据）
					ownerInstance.callMethod('onUserStartBrowsing');

					const handler = myChart.getZr().handler;
					handler.dispatch('mousedown', {
						zrX: x,
						zrY: y
					});
				}
			},
			mouseMove(event, ownerInstance) {
				if (myChart) {
					const rect = event.currentTarget.getBoundingClientRect();
					const x = event.clientX - rect.left;
					const y = event.clientY - rect.top;

					const handler = myChart.getZr().handler;
					handler.dispatch('mousemove', {
						zrX: x,
						zrY: y
					});
				}
			},
			mouseUp(event, ownerInstance) {
				console.log('H5 mouseUp triggered');
				if (myChart) {
					const rect = event.currentTarget.getBoundingClientRect();
					const x = event.clientX - rect.left;
					const y = event.clientY - rect.top;

					const handler = myChart.getZr().handler;
					handler.dispatch('mouseup', {
						zrX: x,
						zrY: y
					});
				}
			},
			wrapTouch(event, rect) {
				const wrappedEvent = Object.assign({}, event);
				wrappedEvent.touches = [];

				for (let i = 0; i < event.touches.length; ++i) {
					const touch = event.touches[i];
					const wrappedTouch = Object.assign({}, touch);
					wrappedTouch.offsetX = touch.clientX - rect.left;
					wrappedTouch.offsetY = touch.clientY - rect.top;
					wrappedTouch.x = wrappedTouch.offsetX;
					wrappedTouch.y = wrappedTouch.offsetY;
					wrappedEvent.touches.push(wrappedTouch);
				}

				return wrappedEvent;
			},
			messageChanged(newVal, oldVal, ins, vm) {
				if(newVal.canvasId){
					this.CcanvasId = newVal.canvasId
					delete newVal.canvasId
					this.Coption = newVal
					this.initEcharts()
				}else{
					myChart.setOption(newVal)
				}
				
			}
		}
	}
	// #endif
</script>




<style scoped>
	.ec-canvas {
		width: 100%;
		height: 100%;
	}
</style>
