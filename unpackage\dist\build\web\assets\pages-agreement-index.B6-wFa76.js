import{H as t,n as e,L as a,s,M as o,f as n,h as r,w as l,i,o as c,j as f,m as d,t as g,z as m,x as p,Q as h,R as u}from"./index-9TNq14KG.js";import{_ as y}from"./uni-icons.Dtv1TTQG.js";import{r as _}from"./uni-app.es.Df5Q6fJy.js";import{r as x}from"./request.B3nfocmy.js";import{_ as H}from"./_plugin-vue_export-helper.BCo6x5W8.js";import"./index.B6QF5Ba_.js";const I=H({data:()=>({statusBarHeight:0,scrollHeight:0,agreementId:"",agreementInfo:{title:"",content:""},from:""}),onLoad(e){const a=t();this.statusBarHeight=a.statusBarHeight,this.scrollHeight=a.windowHeight,this.from=e.from||"",this.agreementId=e.id,this.loadAgreementDetail()},methods:{handleBack(){"A"===this.from?window.history.back():e()},async loadAgreementDetail(){try{a({title:"加载中..."});const t=await x({url:`/api/agreement/detail/${this.agreementId}`,method:"GET"});200===t.code&&t.data&&(this.agreementInfo={title:t.data.title,content:"string"==typeof t.data.content?t.data.content:JSON.stringify(t.data.content)})}catch(t){console.error("获取条款详情失败:",t),s({title:"获取条款详情失败",icon:"none"})}finally{o()}},formatRichText:t=>t?t=(t=(t=(t=(t=t.replace(/color:\s*rgb\(0,\s*0,\s*0\)/gi,"color: #fff")).replace(/color:\s*#000000/gi,"color: #fff")).replace(/color:\s*black/gi,"color: #fff")).replace(/<p>/gi,'<p style="color: #fff;">')).replace(/<span>/gi,'<span style="color: #fff;">'):""}},[["render",function(t,e,a,s,o,x){const H=_(n("uni-icons"),y),I=i,b=p,j=h,B=u;return c(),r(I,{class:"agreement-detail-container"},{default:l((()=>[f(I,{class:"custom-navbar",style:m({paddingTop:o.statusBarHeight+"px"})},{default:l((()=>[f(I,{class:"navbar-content"},{default:l((()=>[f(I,{class:"left-area",onClick:x.handleBack},{default:l((()=>[f(H,{type:"left",size:"20",color:"#fff"})])),_:1},8,["onClick"]),f(b,{class:"page-title"},{default:l((()=>[d(g(o.agreementInfo.title),1)])),_:1}),f(I,{class:"right-area"})])),_:1})])),_:1},8,["style"]),f(B,{class:"content-box","scroll-y":"",style:m({height:o.scrollHeight+"px"})},{default:l((()=>[f(I,{class:"agreement-content"},{default:l((()=>[f(j,{nodes:x.formatRichText(o.agreementInfo.content),class:"content-text"},null,8,["nodes"])])),_:1})])),_:1},8,["style"])])),_:1})}],["__scopeId","data-v-3995c4b8"]]);export{I as default};
