import{g as s,a3 as a,f as e,h as t,w as r,i as o,o as c,j as i,m as l,l as d,q as n,F as m,v as p,x as u,t as h,p as f}from"./index-C9YaP0ep.js";import{C as g}from"./custom-navbar.qFutWWMs.js";import{r as _}from"./uni-app.es.CncftGS-.js";import{c as y}from"./index.B6QF5Ba_.js";import{_ as v}from"./_plugin-vue_export-helper.BCo6x5W8.js";import"./uni-icons.CxVA1Ran.js";const j=v({components:{CustomNavbar:g},data:()=>({records:[],page:1,pageSize:20,hasMore:!0}),onShow(){this.getRecords()},onReachBottom(){this.hasMore&&(this.page++,this.getRecords())},methods:{async getRecords(){try{const e=s("token"),t=await a({url:`${y.baseUrl}/api/share/records`,method:"GET",data:{page:this.page,pageSize:this.pageSize},header:{Authorization:e}});if(0===t.data.code){const s=t.data.data.list||[];1===this.page?this.records=s:this.records=[...this.records,...s],this.hasMore=s.length===this.pageSize}}catch(e){console.error("获取邀请记录失败:",e)}}}},[["render",function(s,a,y,v,j,k){const x=_(e("custom-navbar"),g),z=p,S=u,b=o;return c(),t(b,{class:"container"},{default:r((()=>[i(x,{title:"邀请记录",showBack:!0}),i(b,{class:"content"},{default:r((()=>[j.records.length?(c(),t(b,{key:1,class:"record-list"},{default:r((()=>[(c(!0),d(m,null,n(j.records,((s,a)=>(c(),t(b,{key:a,class:"record-item"},{default:r((()=>[i(b,{class:"user-info"},{default:r((()=>[i(z,{src:s.avatar||"/static/images/default-avatar.png",class:"avatar",mode:"aspectFill"},null,8,["src"]),i(b,{class:"info"},{default:r((()=>[i(S,{class:"nickname"},{default:r((()=>[l(h(s.nickname||"用户"+s.userId),1)])),_:2},1024),i(S,{class:"time"},{default:r((()=>[l(h(s.createTime),1)])),_:2},1024)])),_:2},1024)])),_:2},1024),i(b,{class:f(["status",1===s.status?"success":""])},{default:r((()=>[l(h(1===s.status?"+10算力":"未完成注册"),1)])),_:2},1032,["class"])])),_:2},1024)))),128))])),_:1})):(c(),t(b,{key:0,class:"empty-state"},{default:r((()=>[i(z,{src:"/static/images/empty.png",mode:"aspectFit",class:"empty-image"}),i(S,{class:"empty-text"},{default:r((()=>[l("空空如也")])),_:1})])),_:1}))])),_:1})])),_:1})}],["__scopeId","data-v-13e6413a"]]);export{j as default};
