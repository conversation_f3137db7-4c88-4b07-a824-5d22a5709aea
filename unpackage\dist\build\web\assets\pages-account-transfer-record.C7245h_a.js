import{H as t,n as s,g as e,s as a,f as r,h as o,w as i,z as n,i as c,o as l,j as d,m as u,l as f,q as h,F as g,k as p,N as m,x as y,t as T,p as _}from"./index-DSmyjKbQ.js";import{_ as x}from"./uni-icons.ouCj1myJ.js";import{r as S}from"./uni-app.es.CZvmdujn.js";import{r as v}from"./request.DeqggKcp.js";import{_ as k}from"./_plugin-vue_export-helper.BCo6x5W8.js";import"./index.B6QF5Ba_.js";const B=k({data:()=>({statusBarHeight:0,navBarHeight:44,navPaddingTop:44,transferRecords:[],page:1,pageSize:10,hasMore:!0,isLoading:!1}),created(){const s=t();this.statusBarHeight=s.statusBarHeight,this.navPaddingTop=this.statusBarHeight+this.navBarHeight},onShow(){this.loadTransferRecords(!0)},methods:{handleBack(){s()},async loadTransferRecords(t=!1){var s;if(!this.isLoading&&(t&&(this.page=1,this.hasMore=!0,this.transferRecords=[]),this.hasMore||t))try{this.isLoading=!0;const r=e("token"),o=await v({url:"/api/transfer/records",method:"GET",params:{page:this.page,size:this.pageSize},header:{Authorization:r?`Bearer ${r}`:""}});if(o&&200===o.code){const e=(null==(s=o.data)?void 0:s.records)||[];t?this.transferRecords=e:this.transferRecords.push(...e),this.hasMore=e.length===this.pageSize,this.hasMore&&this.page++}else a({title:(null==o?void 0:o.msg)||"获取记录失败",icon:"none"})}catch(r){console.error("获取划转记录失败:",r),a({title:"获取记录失败",icon:"none"})}finally{this.isLoading=!1}},loadMore(){!this.isLoading&&this.hasMore&&this.loadTransferRecords()},getTransferTypeText:t=>`${t.fromAccountType} -> ${t.toAccountType}`,getStatusText:t=>({0:"处理中",1:"成功",2:"失败"}[t]||"未知"),getStatusClass(t){switch(t){case 0:return"status-pending";case 1:return"status-success";case 2:return"status-fail";default:return"status-default"}},getAccountTypeText:t=>({fund:"资金账户",commission:"佣金账户",copy:"跟单账户",profit:"利润账户"}[t]||t),formatTime(t){if(!t)return"";const s=new Date(t);return`${s.getFullYear()}-${String(s.getMonth()+1).padStart(2,"0")}-${String(s.getDate()).padStart(2,"0")} ${String(s.getHours()).padStart(2,"0")}:${String(s.getMinutes()).padStart(2,"0")}`}}},[["render",function(t,s,e,a,v,k){const B=S(r("uni-icons"),x),M=c,w=y;return l(),o(M,{class:"record-container",style:n({paddingTop:v.navPaddingTop+"px"})},{default:i((()=>[d(M,{class:"custom-navbar",style:n({paddingTop:v.statusBarHeight+"px"})},{default:i((()=>[d(M,{class:"navbar-content"},{default:i((()=>[d(M,{class:"left-area",onClick:k.handleBack},{default:i((()=>[d(B,{type:"left",size:"20",color:"#fff"})])),_:1},8,["onClick"]),d(w,{class:"page-title"},{default:i((()=>[u("划转记录")])),_:1}),d(M,{class:"right-area"})])),_:1})])),_:1},8,["style"]),v.transferRecords.length>0?(l(),o(M,{key:0,class:"record-list"},{default:i((()=>[(l(!0),f(g,null,h(v.transferRecords,((t,s)=>(l(),o(M,{class:"record-item",key:s},{default:i((()=>[d(M,{class:"record-main"},{default:i((()=>[d(M,{class:"record-left"},{default:i((()=>[d(w,{class:"transfer-direction"},{default:i((()=>[u(T(k.getAccountTypeText(t.fromAccountType))+" -> "+T(k.getAccountTypeText(t.toAccountType)),1)])),_:2},1024),d(w,{class:"record-time"},{default:i((()=>[u(T(k.formatTime(t.createTime)),1)])),_:2},1024)])),_:2},1024),d(M,{class:"record-right"},{default:i((()=>[d(w,{class:"amount-value"},{default:i((()=>[u(T(t.amount)+" USDT",1)])),_:2},1024),d(w,{class:_(["record-status",k.getStatusClass(t.status)])},{default:i((()=>[u(T(k.getStatusText(t.status)),1)])),_:2},1032,["class"])])),_:2},1024)])),_:2},1024)])),_:2},1024)))),128)),v.hasMore?(l(),o(M,{key:0,class:"load-more",onClick:k.loadMore},{default:i((()=>[d(w,{class:"load-text"},{default:i((()=>[u("加载更多")])),_:1})])),_:1},8,["onClick"])):p("",!0)])),_:1})):(l(),o(M,{key:1,class:"empty-state"},{default:i((()=>[d(M,{class:"empty-img"},{default:i((()=>[(l(),f("svg",{width:"180",height:"180",viewBox:"0 0 180 180"},[m("rect",{x:"40",y:"80",width:"100",height:"60",rx:"8",fill:"#d6ff3c"}),m("rect",{x:"60",y:"60",width:"60",height:"40",rx:"8",fill:"#d6ff3c",opacity:"0.7"})]))])),_:1}),d(w,{class:"empty-text"},{default:i((()=>[u("暂无划转记录")])),_:1})])),_:1}))])),_:1},8,["style"])}],["__scopeId","data-v-5b7978f4"]]);export{B as default};
