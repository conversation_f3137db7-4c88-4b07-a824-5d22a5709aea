import{a5 as a,H as t,n as s,f as e,h as i,w as l,z as d,i as r,o,j as n,m as c,l as f,N as h,q as u,F as g,k as p,x as m,t as _,p as y}from"./index-C9YaP0ep.js";import{_ as w}from"./uni-icons.CxVA1Ran.js";import{r as x}from"./uni-app.es.CncftGS-.js";import{r as k}from"./request.BgmJQJgX.js";import{_ as T}from"./_plugin-vue_export-helper.BCo6x5W8.js";import"./index.B6QF5Ba_.js";const B=T({data:()=>({statusBarHeight:0,navBarHeight:44,navPaddingTop:44,records:[],page:1,size:10,total:0,loading:!1,finished:!1}),onPullDownRefresh(){this.getRecords(!0),a()},onReachBottom(){this.getRecords()},created(){const a=t();this.statusBarHeight=a.statusBarHeight,this.navPaddingTop=this.statusBarHeight+this.navBarHeight,this.getRecords()},methods:{handleBack(){s()},async getRecords(a=!1){if(!(this.loading||this.finished&&!a)){this.loading=!0,a&&(this.page=1,this.records=[],this.finished=!1);try{const t=await k({url:"/api/withdraw-record/list",method:"GET",data:{page:this.page,size:this.size}});if(200===t.code&&t.data){const{records:s,total:e}=t.data;this.total=e,this.records=a?s:this.records.concat(s),this.records.length>=e?this.finished=!0:this.page+=1}}finally{this.loading=!1}}},formatDateTime:a=>a?a.replace("T"," ").replace(/-/g,"-").slice(0,19):""}},[["render",function(a,t,s,k,T,B){const v=x(e("uni-icons"),w),H=r,j=m;return o(),i(H,{class:"withdraw-record-container",style:d({paddingTop:T.navPaddingTop+"px"})},{default:l((()=>[n(H,{class:"custom-navbar",style:d({paddingTop:T.statusBarHeight+"px"})},{default:l((()=>[n(H,{class:"navbar-content"},{default:l((()=>[n(H,{class:"left-area",onClick:B.handleBack},{default:l((()=>[n(v,{type:"left",size:"20",color:"#fff"})])),_:1},8,["onClick"]),n(j,{class:"page-title"},{default:l((()=>[c("提现记录")])),_:1}),n(H,{class:"right-area"})])),_:1})])),_:1},8,["style"]),0!==T.records.length||T.loading?(o(),i(H,{key:1,class:"record-list"},{default:l((()=>[(o(!0),f(g,null,u(T.records,(a=>(o(),i(H,{key:a.id,class:"record-item"},{default:l((()=>[n(H,{class:"row"},{default:l((()=>[n(j,{class:"amount"},{default:l((()=>[c(_(a.amount)+" USDT",1)])),_:2},1024),n(j,{class:y(["status","status-"+a.status])},{default:l((()=>[c(_(0===a.status?"待审核":1===a.status?"已通过":"已拒绝"),1)])),_:2},1032,["class"])])),_:2},1024),n(H,{class:"row info"},{default:l((()=>[n(j,null,{default:l((()=>[c("到账："+_(a.realAmount)+" USDT",1)])),_:2},1024),n(j,null,{default:l((()=>[c("手续费："+_(a.fee)+" USDT",1)])),_:2},1024)])),_:2},1024),n(H,{class:"row info"},{default:l((()=>[n(j,null,{default:l((()=>[c("地址："+_(a.address),1)])),_:2},1024)])),_:2},1024),n(H,{class:"row info"},{default:l((()=>[n(j,null,{default:l((()=>[c("时间："+_(B.formatDateTime(a.createTime)),1)])),_:2},1024)])),_:2},1024),a.remark?(o(),i(H,{key:0,class:"row info"},{default:l((()=>[n(j,null,{default:l((()=>[c("备注："+_(a.remark),1)])),_:2},1024)])),_:2},1024)):p("",!0)])),_:2},1024)))),128)),T.loading?(o(),i(H,{key:0,class:"loading-text"},{default:l((()=>[c("加载中...")])),_:1})):p("",!0),T.finished?(o(),i(H,{key:1,class:"finished-text"},{default:l((()=>[c("没有更多了")])),_:1})):p("",!0)])),_:1})):(o(),i(H,{key:0,class:"empty-state"},{default:l((()=>[n(H,{class:"empty-img"},{default:l((()=>[(o(),f("svg",{width:"180",height:"180",viewBox:"0 0 180 180"},[h("rect",{x:"40",y:"80",width:"100",height:"60",rx:"8",fill:"#d6ff3c"}),h("rect",{x:"60",y:"60",width:"60",height:"40",rx:"8",fill:"#d6ff3c",opacity:"0.7"})]))])),_:1}),n(j,{class:"empty-text"},{default:l((()=>[c("空空如也")])),_:1})])),_:1}))])),_:1},8,["style"])}],["__scopeId","data-v-1085c33f"]]);export{B as default};
