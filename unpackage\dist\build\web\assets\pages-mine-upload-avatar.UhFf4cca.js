import{O as a,g as t,P as e,s,n as l,h as r,w as i,i as o,o as c,j as d,m as n,v as u,y as p}from"./index-C9YaP0ep.js";import{c as v}from"./index.B6QF5Ba_.js";import{_ as h}from"./_plugin-vue_export-helper.BCo6x5W8.js";const m=h({data:()=>({avatarUrl:"",avatarFile:null,defaultAvatar:"/static/tools/default.png"}),methods:{chooseImage(){a({count:1,sizeType:["compressed"],success:a=>{this.avatarUrl=a.tempFilePaths[0],this.avatarFile=a.tempFiles[0]}})},uploadAvatar(){if(!this.avatarFile)return;const a=t("token");e({url:v.apiBaseUrl+"/upload/avatar",filePath:this.avatarUrl,name:"file",header:{Authorization:a?"Bearer "+a:""},success:a=>{let t=a.data;try{t=JSON.parse(a.data)}catch(e){}200===t.code&&t.data?(this.avatarUrl=t.data.startsWith("http")?t.data:v.apiBaseUrl+t.data,s({title:"上传成功",icon:"success"}),setTimeout((()=>{l()}),1e3)):s({title:t.message||"上传失败",icon:"none"})},fail:()=>{s({title:"上传失败",icon:"none"})}})}}},[["render",function(a,t,e,s,l,v){const h=u,m=o,f=p;return c(),r(m,{class:"upload-avatar-container"},{default:i((()=>[d(m,{class:"avatar-preview"},{default:i((()=>[d(h,{src:l.avatarUrl||l.defaultAvatar,class:"avatar-img"},null,8,["src"])])),_:1}),d(f,{class:"choose-btn",onClick:v.chooseImage},{default:i((()=>[n("选择图片")])),_:1},8,["onClick"]),d(f,{class:"upload-btn",disabled:!l.avatarFile,onClick:v.uploadAvatar},{default:i((()=>[n("上传头像")])),_:1},8,["disabled","onClick"])])),_:1})}],["__scopeId","data-v-2de5998e"]]);export{m as default};
